/* --- Language - Expressive Speech Bubbles --- */
.icon-language .bubble-container {
  width: 65px;
  height: 55px;
  position: relative;
}

.icon-language .bubble-shape {
  position: absolute;
  background-color: var(--humanities-purple);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  animation: morphBubbleExpressive var(--slow-duration) infinite ease-in-out;
  transform-origin: center center;
}

.icon-language .bubble1 {
  width: 45px;
  height: 32px;
  top: 3px;
  left: 0px;
  animation-delay: 0s;
}

.icon-language .bubble1::before {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 14px;
  width: 0;
  height: 0;
  border-left: 9px solid transparent;
  border-right: 9px solid transparent;
  border-top: 10px solid var(--humanities-purple);
  transform-origin: top center;
  animation: wiggleTailLively var(--slow-duration) infinite ease-in-out;
  animation-delay: 0s;
}

.icon-language .bubble2 {
  width: 36px;
  height: 28px;
  bottom: 0px;
  right: 2px;
  background-color: var(--humanities-purple-light);
  color: var(--text-dark);
  animation-delay: -2.5s;
}

.icon-language .bubble2::before {
  content: '';
  position: absolute;
  bottom: -7px;
  right: 12px;
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 8px solid var(--humanities-purple-light);
  transform-origin: top center;
  animation: wiggleTailLively var(--slow-duration) infinite ease-in-out;
  animation-delay: -2.5s;
}

.icon-language .bubble1::after {
  content: '你好';
  animation: fadeTextBubble 6s infinite ease-in-out 0s;
}

.icon-language .bubble2::after {
  content: 'Salut!';
  animation: fadeTextBubble 6s infinite ease-in-out -2.5s;
}

@keyframes morphBubbleExpressive {
  0%, 100% { border-radius: 14px 14px 14px 8px; transform: translateY(0) rotate(-2deg) scale(1); }
  25% { border-radius: 50%; transform: translateY(-4px) rotate(3deg) scale(1.05); }
  50% { border-radius: 8px 14px 8px 14px; transform: translateY(3px) rotate(-4deg) scale(0.97); }
  75% { border-radius: 10px; transform: translateY(-2px) rotate(1deg) scale(1.02); }
}

@keyframes wiggleTailLively {
  0%, 100% { transform: rotate(-8deg) scaleX(1); }
  25% { transform: rotate(8deg) scaleX(1.1); }
  50% { transform: rotate(0deg) scaleX(0.9); }
  75% { transform: rotate(-6deg) scaleX(1.05); }
}

@keyframes fadeTextBubble {
  0%, 10%, 90%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* --- Literature - Animated Book --- */
.icon-literature .book-cover {
  width: 50px;
  height: 60px;
  background: linear-gradient(45deg, #8B4513, #A0522D);
  border-radius: 4px 8px 8px 4px;
  position: relative;
  perspective: 500px;
  box-shadow: 3px 3px 8px rgba(0, 0, 0, 0.2);
  transform: rotateY(-10deg);
}

.icon-literature .page {
  position: absolute;
  width: 94%;
  height: 92%;
  top: 4%;
  left: 3%;
  background-color: #FFFBF0;
  border: 1px solid #F5F5F5;
  transform-origin: left center;
  border-radius: 0 4px 4px 0;
  animation: turnPageRealistic var(--slow-duration) infinite ease-in-out;
  box-shadow: inset 1.5px 0px 3px rgba(0, 0, 0, 0.05);
}

.icon-literature .page1 {
  z-index: 4;
  animation-delay: 0s;
}

.icon-literature .page2 {
  z-index: 3;
  animation-delay: -0.15s;
  background-color: #FFFDF5;
  transform: translateX(0.5px) translateY(0.5px);
}

.icon-literature .page3 {
  z-index: 2;
  animation-delay: -0.3s;
  transform: translateX(1px) translateY(1px);
}

.icon-literature .page4 {
  z-index: 1;
  animation-delay: -0.45s;
  background-color: #FFFDF5;
  transform: translateX(1.5px) translateY(1.5px);
  animation: none;
}

.icon-literature .page::before,
.icon-literature .page::after {
  content: '';
  position: absolute;
  left: 12%;
  right: 12%;
  height: 1.5px;
  background-color: rgba(0, 0, 0, 0.3);
  animation: fadeTextRealistic var(--slow-duration) infinite ease-in-out;
  animation-delay: inherit;
  transform-origin: left center;
}

.icon-literature .page::before {
  top: 25%;
  width: 70%;
}

.icon-literature .page::after {
  top: 45%;
  width: 80%;
}

@keyframes turnPageRealistic {
  0%, 10% { transform: rotateY(0deg) translateX(var(--tx, 0px)) translateY(var(--ty, 0px)); }
  50% { transform: rotateY(-175deg) translateX(var(--tx, 0px)) translateY(var(--ty, 0px)); }
  90%, 100% { transform: rotateY(0deg) translateX(var(--tx, 0px)) translateY(var(--ty, 0px)); }
}

.icon-literature .page2 {
  --tx: 0.5px;
  --ty: 0.5px;
}

.icon-literature .page3 {
  --tx: 1px;
  --ty: 1px;
}

@keyframes fadeTextRealistic {
  0%, 15%, 85%, 100% { opacity: 0; transform: scaleX(0.8); }
  50% { opacity: 0.6; transform: scaleX(1); }
}

/* --- Mathematics - Glowing Pi Symbol --- */
.icon-math .pi-container {
  width: 55px;
  height: 55px;
  position: relative;
  perspective: 600px;
}

.icon-math .pi-symbol {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotateY(0deg);
  font-size: 52px;
  font-weight: 500;
  font-family: 'Georgia', 'Times New Roman', Times, serif;
  color: var(--humanities-purple);
  text-shadow: 0 0 10px var(--humanities-purple-light), 0 0 20px rgba(124, 58, 237, 0.6);
  animation: pulsePiDynamic var(--medium-duration) infinite ease-in-out;
  z-index: 2;
}

.icon-math .orbiting-num {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 18px;
  height: 18px;
  margin-left: -9px;
  margin-top: -9px;
  color: var(--humanities-purple);
  font-size: 10px;
  font-weight: bold;
  text-align: center;
  line-height: 18px;
  animation: orbitPiDynamic var(--slow-duration) infinite linear;
  transform-style: preserve-3d;
  z-index: 1;
  opacity: 0.9;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  box-shadow: 0 0 3px rgba(124, 58, 237, 0.3);
}

.icon-math .num1 {
  animation-delay: 0s;
}

.icon-math .num2 {
  animation-delay: -0.8s;
}

.icon-math .num3 {
  animation-delay: -1.6s;
}

.icon-math .num4 {
  animation-delay: -2.4s;
}

.icon-math .num5 {
  animation-delay: -3.2s;
}

@keyframes pulsePiDynamic {
  0%, 100% { transform: translate(-50%, -50%) scale(1) rotateY(0deg); text-shadow: 0 0 10px var(--humanities-purple-light), 0 0 20px rgba(124, 58, 237, 0.6); }
  50% { transform: translate(-50%, -50%) scale(1.1) rotateY(15deg); text-shadow: 0 0 15px var(--humanities-purple), 0 0 30px var(--humanities-purple); }
}

@keyframes orbitPiDynamic {
  from { transform: rotateY(0deg) translateX(30px) rotateY(0deg) rotateX(15deg) translateZ(5px); opacity: 0.7; }
  to { transform: rotateY(360deg) translateX(30px) rotateY(-360deg) rotateX(15deg) translateZ(5px); opacity: 0.9; }
}
