/* --- Psychology - Advanced Mind Visualization --- */
.icon-psychology .mind-container {
  width: 60px;
  height: 60px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 500px;
}

.icon-psychology .brain {
  width: 42px;
  height: 36px;
  background: linear-gradient(135deg, var(--prussian-blue) 0%, var(--gray-blue) 100%);
  border-radius: 22px 22px 16px 16px;
  position: relative;
  box-shadow: 
    inset 0 -5px 10px rgba(0, 48, 73, 0.3),
    0 4px 8px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  z-index: 1;
  transform-style: preserve-3d;
  animation: pulseThinking 5s infinite alternate ease-in-out;
}

/* Brain hemispheres */
.icon-psychology .hemisphere {
  position: absolute;
  top: -5px;
  width: 24px;
  height: 32px;
  border-radius: 50%;
  z-index: 2;
  background: linear-gradient(135deg, var(--gray-blue) 0%, var(--prussian-blue) 100%);
}

.icon-psychology .hemisphere-left {
  left: -4px;
  box-shadow: inset -3px -3px 6px rgba(0, 48, 73, 0.3);
  animation: pulsateLeft 4s infinite alternate;
}

.icon-psychology .hemisphere-right {
  right: -4px;
  box-shadow: inset 3px -3px 6px rgba(0, 48, 73, 0.3);
  animation: pulsateRight 4s infinite alternate-reverse;
}

/* Brain texture */
.icon-psychology .cortex {
  position: absolute;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 30% 30%, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 20%),
    radial-gradient(circle at 70% 60%, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 20%);
  z-index: 3;
  opacity: 0.7;
}

/* Brain divider */
.icon-psychology .brain-divider {
  position: absolute;
  width: 2px;
  height: 80%;
  top: 10%;
  left: 50%;
  margin-left: -1px;
  background: linear-gradient(to bottom, var(--orange-peel), var(--maize));
  z-index: 3;
  box-shadow: 0 0 5px rgba(247, 127, 0, 0.5);
}

/* Thought bubble */
.icon-psychology .thought {
  position: absolute;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  top: 5px;
  right: 2px;
  z-index: 5;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  animation: floatThought 6s infinite ease-in-out;
  opacity: 0.9;
}

.icon-psychology .thought::before,
.icon-psychology .thought::after {
  content: '';
  position: absolute;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
}

.icon-psychology .thought::before {
  width: 8px;
  height: 8px;
  bottom: -4px;
  right: 2px;
}

.icon-psychology .thought::after {
  width: 4px;
  height: 4px;
  bottom: -8px;
  right: 0px;
}

/* Neurons and connections */
.icon-psychology .neuron {
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: var(--cinnabar);
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(214, 40, 40, 0.6);
  z-index: 4;
  animation: pulseNeuron 3s infinite;
}

.icon-psychology .neuron-1 {
  top: 20%;
  left: 25%;
  animation-delay: 0.2s;
}

.icon-psychology .neuron-2 {
  top: 60%;
  left: 20%;
  animation-delay: 1.2s;
}

.icon-psychology .neuron-3 {
  top: 40%;
  left: 70%;
  animation-delay: 0.8s;
}

.icon-psychology .neuron-4 {
  top: 70%;
  left: 75%;
  animation-delay: 1.6s;
}

.icon-psychology .neuron-5 {
  top: 30%;
  left: 40%;
  animation-delay: 0.4s;
}

.icon-psychology .synapse {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, var(--cinnabar), var(--orange-peel));
  z-index: 3;
  transform-origin: left center;
  opacity: 0;
  animation: fireSignalNeural 3s infinite;
}

.icon-psychology .synapse-1 {
  width: 20px;
  top: 22%;
  left: 28%;
  transform: rotate(30deg);
  animation-delay: 0.2s;
}

.icon-psychology .synapse-2 {
  width: 25px;
  top: 40%;
  left: 43%;
  transform: rotate(-15deg);
  animation-delay: 0.6s;
}

.icon-psychology .synapse-3 {
  width: 15px;
  top: 62%;
  left: 23%;
  transform: rotate(-45deg);
  animation-delay: 1.0s;
}

.icon-psychology .synapse-4 {
  width: 20px;
  top: 65%;
  left: 55%;
  transform: rotate(15deg);
  animation-delay: 1.4s;
}

.icon-psychology .synapse-5 {
  width: 15px;
  top: 35%;
  left: 25%;
  transform: rotate(75deg);
  animation-delay: 1.8s;
}

/* Animated thought content */
.icon-psychology .thought-content {
  position: absolute;
  width: 10px;
  height: 2px;
  background-color: var(--prussian-blue);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.8;
  animation: animateThought 4s infinite;
}

.icon-psychology .thought-content::before,
.icon-psychology .thought-content::after {
  content: '';
  position: absolute;
  background-color: var(--prussian-blue);
  height: 2px;
  width: 6px;
  top: -4px;
  left: 2px;
}

.icon-psychology .thought-content::after {
  top: 4px;
}

@keyframes pulseThinking {
  0%, 100% { transform: scale(0.95) rotateX(0deg); }
  50% { transform: scale(1) rotateX(5deg); }
}

@keyframes pulsateLeft {
  0% { transform: scale(0.95); }
  100% { transform: scale(1.05); }
}

@keyframes pulsateRight {
  0% { transform: scale(0.95); }
  100% { transform: scale(1.05); }
}

@keyframes floatThought {
  0%, 100% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-5px) scale(1.1); }
}

@keyframes pulseNeuron {
  0%, 100% { transform: scale(0.8); opacity: 0.7; }
  50% { transform: scale(1.2); opacity: 1; box-shadow: 0 0 12px rgba(214, 40, 40, 0.8); }
}

@keyframes fireSignalNeural {
  0%, 100% { opacity: 0; transform-origin: left center; transform: scaleX(0.1); }
  10% { opacity: 0.3; transform: scaleX(0.2); }
  50% { opacity: 1; transform: scaleX(1); }
  90% { opacity: 0.3; transform: scaleX(0.2); }
}

@keyframes animateThought {
  0%, 100% { width: 10px; }
  25% { width: 6px; }
  50% { width: 12px; }
  75% { width: 8px; }
}

/* --- Sociology - Connected People --- */
.icon-sociology .people-container {
  width: 60px;
  height: 60px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-sociology .person {
  position: absolute;
  z-index: 2;
}

.icon-sociology .person-head {
  width: 10px;
  height: 10px;
  background-color: var(--deep-red);
  border-radius: 50%;
  position: relative;
}

.icon-sociology .person-body {
  width: 2px;
  height: 12px;
  background-color: var(--deep-red);
  position: absolute;
  top: 9px;
  left: 50%;
  margin-left: -1px;
}

.icon-sociology .person-arm {
  width: 8px;
  height: 2px;
  background-color: var(--deep-red);
  position: absolute;
  top: 12px;
  left: 50%;
  margin-left: -4px;
}

.icon-sociology .person-leg {
  width: 2px;
  height: 8px;
  background-color: var(--deep-red);
  position: absolute;
  top: 20px;
}

.icon-sociology .person-leg-left {
  left: 50%;
  margin-left: -3px;
  transform: rotate(-15deg);
}

.icon-sociology .person-leg-right {
  left: 50%;
  margin-left: 1px;
  transform: rotate(15deg);
}

.icon-sociology .person-1 {
  top: 10px;
  left: 15px;
  animation: movePerson1Connected var(--medium-duration) infinite alternate ease-in-out;
}

.icon-sociology .person-2 {
  top: 15px;
  right: 15px;
  animation: movePerson2Connected var(--medium-duration) infinite alternate ease-in-out;
}

.icon-sociology .person-3 {
  bottom: 10px;
  left: 20px;
  animation: movePerson3Connected var(--medium-duration) infinite alternate ease-in-out;
}

.icon-sociology .person-4 {
  bottom: 15px;
  right: 20px;
  animation: movePerson4Connected var(--medium-duration) infinite alternate ease-in-out;
}

.icon-sociology .person-5 {
  top: 50%;
  left: 50%;
  margin-top: -15px;
  margin-left: -5px;
  animation: movePerson5Connected var(--medium-duration) infinite alternate ease-in-out;
}

.icon-sociology .connection {
  position: absolute;
  background-color: var(--humanities-purple-light);
  z-index: 1;
  opacity: 0.6;
}

.icon-sociology .connection-1 {
  width: 25px;
  height: 2px;
  top: 15px;
  left: 22px;
  transform: rotate(10deg);
}

.icon-sociology .connection-2 {
  width: 25px;
  height: 2px;
  top: 25px;
  right: 22px;
  transform: rotate(-20deg);
}

.icon-sociology .connection-3 {
  width: 20px;
  height: 2px;
  bottom: 25px;
  left: 25px;
  transform: rotate(-30deg);
}

.icon-sociology .connection-4 {
  width: 20px;
  height: 2px;
  bottom: 20px;
  right: 25px;
  transform: rotate(30deg);
}

.icon-sociology .connection-pulse {
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: var(--white);
  border-radius: 50%;
  box-shadow: 0 0 8px var(--humanities-purple);
  opacity: 0;
  z-index: 3;
  animation: pulseSocialConnected var(--medium-duration) infinite;
}

.icon-sociology .pulse-1 {
  top: 14px;
  left: 30px;
  animation-delay: 0.2s;
}

.icon-sociology .pulse-2 {
  top: 24px;
  right: 30px;
  animation-delay: 0.6s;
}

.icon-sociology .pulse-3 {
  bottom: 24px;
  left: 30px;
  animation-delay: 1.0s;
}

.icon-sociology .pulse-4 {
  bottom: 19px;
  right: 30px;
  animation-delay: 1.4s;
}

@keyframes movePerson1Connected {
  0% {
    transform: translateY(0) translateX(0);
  }
  100% {
    transform: translateY(2px) translateX(2px);
  }
}

@keyframes movePerson2Connected {
  0% {
    transform: translateY(0) translateX(0);
  }
  100% {
    transform: translateY(2px) translateX(-2px);
  }
}

@keyframes movePerson3Connected {
  0% {
    transform: translateY(0) translateX(0);
  }
  100% {
    transform: translateY(-2px) translateX(2px);
  }
}

@keyframes movePerson4Connected {
  0% {
    transform: translateY(0) translateX(0);
  }
  100% {
    transform: translateY(-2px) translateX(-2px);
  }
}

@keyframes movePerson5Connected {
  0% {
    transform: translateY(0) scale(1);
  }
  100% {
    transform: translateY(2px) scale(1.1);
  }
}

@keyframes pulseSocialConnected {
  0%, 100% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* --- Statistics - Data Visualization --- */
.icon-statistics .stats-container {
  width: 60px;
  height: 60px;
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.icon-statistics .axis-x,
.icon-statistics .axis-y {
  position: absolute;
  background-color: var(--grey-dark);
}

.icon-statistics .axis-x {
  width: 50px;
  height: 2px;
  bottom: 15px;
  left: 5px;
}

.icon-statistics .axis-y {
  width: 2px;
  height: 40px;
  bottom: 15px;
  left: 5px;
}

.icon-statistics .bell-curve {
  position: absolute;
  width: 50px;
  height: 30px;
  bottom: 16px;
  left: 5px;
  overflow: visible;
}

.icon-statistics .curve-path {
  fill: none;
  stroke: var(--prussian-blue);
  stroke-width: 2;
  stroke-linecap: round;
  filter: drop-shadow(0 2px 3px rgba(0, 48, 73, 0.3));
  animation: drawCurveData var(--medium-duration) infinite alternate ease-in-out;
}

.icon-statistics .data-point {
  position: absolute;
  width: 4px;
  height: 4px;
  background-color: var(--math-sci-blue-light);
  border: 1px solid var(--math-sci-blue);
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(37, 99, 235, 0.4);
  opacity: 0;
  animation: showPointData var(--medium-duration) infinite;
}

.icon-statistics .data-point-1 {
  bottom: 20px;
  left: 10px;
  animation-delay: 0.1s;
}

.icon-statistics .data-point-2 {
  bottom: 25px;
  left: 15px;
  animation-delay: 0.3s;
}

.icon-statistics .data-point-3 {
  bottom: 30px;
  left: 20px;
  animation-delay: 0.5s;
}

.icon-statistics .data-point-4 {
  bottom: 35px;
  left: 25px;
  animation-delay: 0.7s;
}

.icon-statistics .data-point-5 {
  bottom: 30px;
  left: 30px;
  animation-delay: 0.9s;
}

.icon-statistics .data-point-6 {
  bottom: 25px;
  left: 35px;
  animation-delay: 1.1s;
}

.icon-statistics .data-point-7 {
  bottom: 20px;
  left: 40px;
  animation-delay: 1.3s;
}

.icon-statistics .mean-line {
  position: absolute;
  width: 2px;
  height: 25px;
  bottom: 15px;
  left: 25px;
  background-color: var(--accent-red);
  opacity: 0;
  animation: showMeanData var(--medium-duration) infinite;
  animation-delay: 1.5s;
}

.icon-statistics .mean-label {
  position: absolute;
  bottom: 42px;
  left: 21px;
  font-size: 8px;
  font-weight: bold;
  color: var(--accent-red);
  opacity: 0;
  animation: showMeanData var(--medium-duration) infinite;
  animation-delay: 1.7s;
}

.icon-statistics .mean-label::before {
  content: 'μ';
}

@keyframes drawCurveData {
  0% {
    stroke-dasharray: 100;
    stroke-dashoffset: 100;
  }
  100% {
    stroke-dasharray: 100;
    stroke-dashoffset: 0;
  }
}

@keyframes showPointData {
  0%, 100% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes showMeanData {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}
