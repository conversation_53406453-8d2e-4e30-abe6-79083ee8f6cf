/* --- Geography - Interactive 3D Globe --- */
.icon-geography .globe-container {
  width: 60px;
  height: 60px;
  position: relative;
  perspective: 800px;
}

.icon-geography .globe {
  width: 40px;
  height: 40px;
  position: absolute;
  top: 5px;
  left: 50%;
  margin-left: -20px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, var(--gray-blue) 0%, var(--prussian-blue) 70%, var(--dark-slate) 100%);
  box-shadow: 
    inset 0 0 15px rgba(0, 0, 0, 0.2),
    0 0 10px rgba(0, 48, 73, 0.4);
  transform-style: preserve-3d;
  animation: rotateGlobeInteractive var(--slow-duration) infinite linear;
  z-index: 2;
}

.icon-geography .continents {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath fill='%23EAE2B7' d='M30,30 Q40,20 50,30 T70,40 Q75,50 65,60 T40,70 Q30,65 25,50 T30,30'/%3E%3Cpath fill='%23EAE2B7' d='M75,25 Q80,35 70,40 T65,30'/%3E%3Cpath fill='%23EAE2B7' d='M40,20 Q45,15 50,20 T45,25'/%3E%3C/svg%3E");
  background-size: cover;
  opacity: 0.8;
  animation: rotateContinentsInteractive var(--slow-duration) infinite linear;
}

.icon-geography .grid {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 15px rgba(37, 99, 235, 0.3);
  background-image: 
    linear-gradient(transparent 46%, rgba(255, 255, 255, 0.4) 47%, rgba(255, 255, 255, 0.4) 53%, transparent 54%),
    linear-gradient(90deg, transparent 46%, rgba(255, 255, 255, 0.4) 47%, rgba(255, 255, 255, 0.4) 53%, transparent 54%),
    linear-gradient(transparent 21%, rgba(255, 255, 255, 0.2) 22%, rgba(255, 255, 255, 0.2) 28%, transparent 29%),
    linear-gradient(transparent 71%, rgba(255, 255, 255, 0.2) 72%, rgba(255, 255, 255, 0.2) 78%, transparent 79%),
    linear-gradient(90deg, transparent 21%, rgba(255, 255, 255, 0.2) 22%, rgba(255, 255, 255, 0.2) 28%, transparent 29%),
    linear-gradient(90deg, transparent 71%, rgba(255, 255, 255, 0.2) 72%, rgba(255, 255, 255, 0.2) 78%, transparent 79%);
  opacity: 0.7;
}

.icon-geography .stand {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 20px;
  height: 15px;
  margin-left: -10px;
  background: linear-gradient(to bottom, #9CA3AF, #4B5563);
  border-radius: 3px;
  z-index: 1;
  transform: perspective(800px) rotateX(30deg);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.icon-geography .stand::before {
  content: '';
  position: absolute;
  top: -12px;
  left: 50%;
  width: 2px;
  height: 12px;
  margin-left: -1px;
  background: linear-gradient(to bottom, #9CA3AF, #4B5563);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.icon-geography .satellite {
  position: absolute;
  width: 6px;
  height: 3px;
  background-color: #E5E7EB;
  border-radius: 3px;
  transform-style: preserve-3d;
  animation: orbitSatelliteInteractive var(--medium-duration) infinite linear;
  z-index: 3;
}

.icon-geography .satellite::before {
  content: '';
  position: absolute;
  width: 8px;
  height: 1px;
  top: 1px;
  left: -1px;
  background-color: #9CA3AF;
  transform: rotateZ(90deg);
}

.icon-geography .satellite1 {
  animation-delay: -1s;
}

.icon-geography .satellite2 {
  animation-delay: -2s;
}

.icon-geography .cloud {
  position: absolute;
  width: 10px;
  height: 5px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 5px;
  filter: blur(1px);
  opacity: 0.8;
  animation: driftCloudInteractive 8s infinite linear;
  z-index: 4;
}

.icon-geography .cloud1 {
  top: 15px;
  left: 15px;
  animation-delay: -2s;
}

.icon-geography .cloud2 {
  top: 25px;
  right: 10px;
  width: 12px;
  animation-delay: -5s;
}

.icon-geography .cloud3 {
  bottom: 20px;
  left: 20px;
  width: 8px;
  animation-delay: -7s;
}

@keyframes rotateGlobeInteractive {
  0% { transform: rotateY(0deg) rotateX(20deg); }
  100% { transform: rotateY(360deg) rotateX(20deg); }
}

@keyframes rotateContinentsInteractive {
  0% { transform: rotateY(0deg) rotateX(0deg); }
  100% { transform: rotateY(360deg) rotateX(0deg); }
}

@keyframes orbitSatelliteInteractive {
  0% { 
    transform: rotateZ(0deg) translateX(30px) rotateZ(0deg) rotateX(30deg); 
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% { 
    transform: rotateZ(360deg) translateX(30px) rotateZ(-360deg) rotateX(30deg);
    opacity: 0;
  }
}

@keyframes driftCloudInteractive {
  0% { 
    transform: translateX(0) translateY(0) scale(1);
    opacity: 0;
  }
  10% {
    opacity: 0.8;
  }
  50% {
    transform: translateX(10px) translateY(5px) scale(1.1);
  }
  90% {
    opacity: 0.8;
  }
  100% { 
    transform: translateX(0) translateY(0) scale(1);
    opacity: 0;
  }
}

/* --- History - Interactive Timeline --- */
.icon-history .timeline-container {
  width: 60px;
  height: 60px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 600px;
}

.icon-history .timeline-scroll {
  width: 45px;
  height: 55px;
  position: relative;
  background: linear-gradient(to bottom, var(--bone) 0%, var(--bone) 100%);
  border-radius: 5px;
  box-shadow: 
    0 3px 6px rgba(0, 0, 0, 0.2),
    inset 0 0 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transform-style: preserve-3d;
  animation: unfurlScroll 8s infinite ease-in-out;
}

.icon-history .timeline-scroll::before,
.icon-history .timeline-scroll::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 6px;
  background: var(--deep-red);
  left: 0;
  border-radius: 3px;
}

.icon-history .timeline-scroll::before {
  top: 0;
}

.icon-history .timeline-scroll::after {
  bottom: 0;
}

.icon-history .timeline-line {
  position: absolute;
  width: 2px;
  height: 80%;
  background: var(--prussian-blue);
  left: 50%;
  top: 10%;
  transform: translateX(-50%);
}

.icon-history .event {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--cinnabar);
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  box-shadow: 0 0 5px rgba(214, 40, 40, 0.5);
}

.icon-history .event-1 {
  top: 15%;
  animation: pulseEvent 3s infinite 0.5s;
}

.icon-history .event-2 {
  top: 35%;
  animation: pulseEvent 3s infinite 1s;
}

.icon-history .event-3 {
  top: 55%;
  animation: pulseEvent 3s infinite 1.5s;
}

.icon-history .event-4 {
  top: 75%;
  animation: pulseEvent 3s infinite 2s;
}

.icon-history .event-label {
  position: absolute;
  height: 2px;
  background: var(--orange-peel);
  z-index: 1;
  transform-origin: left center;
}

.icon-history .event-label-1 {
  width: 15px;
  top: 15%;
  left: 50%;
  transform: translateY(3px);
  animation: slideLabel 8s infinite 0.5s;
}

.icon-history .event-label-2 {
  width: 12px;
  top: 35%;
  right: 50%;
  transform: translateY(3px) scaleX(-1);
  animation: slideLabel 8s infinite 1s;
}

.icon-history .event-label-3 {
  width: 14px;
  top: 55%;
  left: 50%;
  transform: translateY(3px);
  animation: slideLabel 8s infinite 1.5s;
}

.icon-history .event-label-4 {
  width: 13px;
  top: 75%;
  right: 50%;
  transform: translateY(3px) scaleX(-1);
  animation: slideLabel 8s infinite 2s;
}

.icon-history .date {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 1px;
  background: var(--prussian-blue);
  opacity: 0.7;
}

.icon-history .date-1 {
  top: 15%;
  right: 5px;
  animation: fadeDate 8s infinite 0.5s;
}

.icon-history .date-2 {
  top: 35%;
  left: 5px;
  animation: fadeDate 8s infinite 1s;
}

.icon-history .date-3 {
  top: 55%;
  right: 5px;
  animation: fadeDate 8s infinite 1.5s;
}

.icon-history .date-4 {
  top: 75%;
  left: 5px;
  animation: fadeDate 8s infinite 2s;
}

@keyframes unfurlScroll {
  0%, 100% { transform: rotateY(0deg) rotateX(0deg); }
  25% { transform: rotateY(15deg) rotateX(5deg); }
  50% { transform: rotateY(0deg) rotateX(-5deg); }
  75% { transform: rotateY(-15deg) rotateX(5deg); }
}

@keyframes pulseEvent {
  0%, 100% { transform: translateX(-50%) scale(0.8); opacity: 0.7; }
  50% { transform: translateX(-50%) scale(1.2); opacity: 1; }
}

@keyframes slideLabel {
  0%, 100% { width: 0; opacity: 0; }
  10% { width: 0; opacity: 0; }
  20%, 80% { width: 15px; opacity: 1; }
  90% { width: 0; opacity: 0; }
}

@keyframes fadeDate {
  0%, 100% { opacity: 0; transform: scale(0.8); }
  20%, 80% { opacity: 0.7; transform: scale(1); }
}

/* --- Human Biology - Beating Heart --- */
.icon-human-bio .heart-container {
  width: 60px;
  height: 60px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-human-bio .heart {
  width: 35px;
  height: 35px;
  position: relative;
  transform: rotate(45deg);
  background-color: var(--cinnabar);
  animation: heartbeatBeating var(--medium-duration) infinite cubic-bezier(0.66, 0, 0.33, 1);
  box-shadow: 
    0 0 40px rgba(239, 68, 68, 0.4),
    0 0 15px rgba(239, 68, 68, 0.6);
  z-index: 1;
}

.icon-human-bio .heart::before,
.icon-human-bio .heart::after {
  content: '';
  position: absolute;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background-color: var(--cinnabar);
}

.icon-human-bio .heart::before {
  top: -17.5px;
  left: 0;
}

.icon-human-bio .heart::after {
  top: 0;
  left: -17.5px;
}

.icon-human-bio .ecg-line {
  position: absolute;
  width: 100%;
  height: 20px;
  top: 50%;
  margin-top: -10px;
  display: flex;
  align-items: center;
  z-index: 0;
}

.icon-human-bio .ecg-path {
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, transparent, var(--grey-light), transparent);
  position: relative;
  overflow: hidden;
}

.icon-human-bio .ecg-wave {
  position: absolute;
  width: 200%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, transparent 40%, 
    var(--accent-red) 40%, var(--accent-red) 42%, 
    transparent 42%, transparent 44%, 
    var(--accent-red) 44%, var(--accent-red) 46%, 
    var(--white) 46%, var(--white) 48%, 
    var(--accent-red) 48%, var(--accent-red) 50%, 
    transparent 50%, transparent 100%
  );
  animation: ecgWaveBeating var(--medium-duration) infinite linear;
  left: 0;
}

@keyframes heartbeatBeating {
  0%, 100% {
    transform: rotate(45deg) scale(0.8);
  }
  15% {
    transform: rotate(45deg) scale(1);
  }
  30% {
    transform: rotate(45deg) scale(0.9);
  }
  45% {
    transform: rotate(45deg) scale(1.1);
  }
  60% {
    transform: rotate(45deg) scale(0.9);
  }
}

@keyframes ecgWaveBeating {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* --- ICT - Networked Grid --- */
.icon-ict .network-container {
  width: 60px;
  height: 60px;
  position: relative;
  perspective: 500px;
}

.icon-ict .grid {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 8px;
  transform: rotateX(20deg) rotateZ(5deg);
  transform-style: preserve-3d;
  animation: rotateGridNetworked var(--slow-duration) infinite alternate ease-in-out;
}

.icon-ict .node {
  width: 100%;
  height: 100%;
  background-color: var(--math-sci-blue-light);
  border-radius: 4px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    inset 0 0 5px rgba(37, 99, 235, 0.4),
    0 2px 4px rgba(0, 0, 0, 0.1);
  animation: pulseNodeNetworked var(--medium-duration) infinite alternate;
}

.icon-ict .node::before {
  content: '';
  width: 4px;
  height: 4px;
  background-color: var(--math-sci-blue);
  border-radius: 50%;
  box-shadow: 0 0 8px var(--math-sci-blue);
}

.icon-ict .node:nth-child(1) { animation-delay: 0.0s; }
.icon-ict .node:nth-child(2) { animation-delay: 0.2s; }
.icon-ict .node:nth-child(3) { animation-delay: 0.4s; }
.icon-ict .node:nth-child(4) { animation-delay: 0.6s; }
.icon-ict .node:nth-child(5) { animation-delay: 0.8s; }
.icon-ict .node:nth-child(6) { animation-delay: 1.0s; }
.icon-ict .node:nth-child(7) { animation-delay: 1.2s; }
.icon-ict .node:nth-child(8) { animation-delay: 1.4s; }
.icon-ict .node:nth-child(9) { animation-delay: 1.6s; }

.icon-ict .connection {
  position: absolute;
  background-color: var(--math-sci-blue);
  opacity: 0;
  z-index: 1;
  animation: showConnectionNetworked var(--fast-duration) infinite;
}

.icon-ict .connection-h {
  height: 2px;
  width: 8px;
  top: 50%;
  margin-top: -1px;
}

.icon-ict .connection-v {
  width: 2px;
  height: 8px;
  left: 50%;
  margin-left: -1px;
}

.icon-ict .connection-1 {
  left: calc(33.3% + 4px);
  animation-delay: 0.1s;
}

.icon-ict .connection-2 {
  left: calc(66.6% + 4px);
  animation-delay: 0.3s;
}

.icon-ict .connection-3 {
  top: calc(33.3% + 4px);
  animation-delay: 0.5s;
}

.icon-ict .connection-4 {
  top: calc(66.6% + 4px);
  animation-delay: 0.7s;
}

.icon-ict .data-packet {
  position: absolute;
  width: 4px;
  height: 4px;
  background-color: var(--white);
  border-radius: 50%;
  box-shadow: 0 0 5px var(--math-sci-blue);
  z-index: 2;
  opacity: 0;
  animation: movePacketNetworked var(--medium-duration) infinite;
}

.icon-ict .data-packet-1 {
  top: 50%;
  left: 0;
  margin-top: -2px;
  animation-delay: 0.2s;
}

.icon-ict .data-packet-2 {
  top: 0;
  left: 50%;
  margin-left: -2px;
  animation-delay: 0.6s;
}

.icon-ict .data-packet-3 {
  top: 50%;
  right: 0;
  margin-top: -2px;
  animation-delay: 1.0s;
}

.icon-ict .data-packet-4 {
  bottom: 0;
  left: 50%;
  margin-left: -2px;
  animation-delay: 1.4s;
}

@keyframes rotateGridNetworked {
  0% {
    transform: rotateX(20deg) rotateZ(5deg);
  }
  100% {
    transform: rotateX(30deg) rotateZ(-5deg);
  }
}

@keyframes pulseNodeNetworked {
  0%, 100% {
    background-color: var(--math-sci-blue-light);
    box-shadow: 
      inset 0 0 5px rgba(37, 99, 235, 0.4),
      0 2px 4px rgba(0, 0, 0, 0.1);
  }
  50% {
    background-color: #93C5FD;
    box-shadow: 
      inset 0 0 10px rgba(37, 99, 235, 0.6),
      0 4px 8px rgba(0, 0, 0, 0.15);
  }
}

@keyframes showConnectionNetworked {
  0%, 100% {
    opacity: 0;
  }
  40%, 60% {
    opacity: 0.8;
  }
}

@keyframes movePacketNetworked {
  0%, 100% {
    opacity: 0;
    transform: scale(0.5);
  }
  10% {
    opacity: 1;
    transform: scale(1);
  }
  45% {
    opacity: 1;
    transform: translateX(60px) scale(1);
  }
  50% {
    opacity: 0;
    transform: translateX(60px) scale(0.5);
  }
}
