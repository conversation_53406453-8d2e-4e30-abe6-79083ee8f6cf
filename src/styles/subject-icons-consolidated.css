/* --- Base Variables and Styles --- */
:root {
  /* Primary Colors from color-codes.md */
  --prussian-blue: #003049;
  --cinnabar: #D62828;
  --orange-peel: #F77F00;
  --maize: #FCBF49;
  --bone: #EAE2B7;
  
  /* Secondary Colors from color-codes.md */
  --burnt-sienna: #e56b49;
  --dark-slate: #2b2e44;
  --deep-red: #953137;
  --gray-blue: #a1a4b0;
  --taupe-gray: #a0949c;
  
  /* Original white/black values for contrast */
  --white: #FFFFFF;
  --night: #151616;
  
  /* Subject Group Palettes - Using new color scheme */
  --math-sci-blue: var(--prussian-blue);
  --math-sci-blue-light: var(--gray-blue);
  --bio-chem-green: var(--dark-slate);
  --bio-chem-green-light: var(--bone);
  --business-econ-orange: var(--orange-peel);
  --business-econ-orange-light: var(--maize);
  --humanities-purple: var(--deep-red);
  --humanities-purple-light: var(--taupe-gray);
  --accent-pink: var(--burnt-sienna);
  --accent-red: var(--cinnabar);
  
  /* Professional Greys */
  --grey-light: #F3F4F6;
  --grey-medium: var(--gray-blue);
  --grey-dark: var(--dark-slate);
  --border-light: var(--bone);
  --text-dark: var(--prussian-blue);

  /* Animation Durations */
  --fast-duration: 2.5s;
  --medium-duration: 4s;
  --slow-duration: 6s;
  --very-slow-duration: 10s;
}

/* --- Common Icon Wrapper --- */
.icon-wrapper {
  width: 90px;
  height: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  font-family: 'Inter', sans-serif;
  background-color: var(--white);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid var(--border-light);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.04);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.icon-wrapper:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08), 0 2px 6px rgba(0, 0, 0, 0.05);
}

/* --- Accounting - Dynamic Calculator --- */
.icon-accounting .calculator-body {
  width: 50px;
  height: 60px;
  background: linear-gradient(145deg, #f9fafb, #e5e7eb);
  border-radius: 8px;
  position: relative;
  border: 1px solid var(--border-light);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.06), 0 3px 6px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 6px;
}

.icon-accounting .screen {
  width: 40px;
  height: 14px;
  background-color: #D1FAE5;
  border-radius: 4px;
  margin-bottom: 6px;
  font-size: 9px;
  font-family: 'Courier New', Courier, monospace;
  color: var(--text-dark);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 4px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.icon-accounting .screen::after {
  content: '1,234';
  position: absolute;
  right: 4px;
  animation: calcNumbersDynamic var(--slow-duration) infinite steps(8, end);
}

@keyframes calcNumbersDynamic {
  0% { content: '1,234'; }
  12% { content: '+ 567'; }
  24% { content: '= 1,801'; }
  36% { content: '/ 7'; }
  48% { content: '= 257.28'; }
  60% { content: '* 10'; }
  72% { content: '= 2,572'; }
  84% { content: 'TOTAL'; }
  100% { content: '2,572'; }
}

.icon-accounting .buttons {
  display: grid;
  grid-template-columns: repeat(4, 10px);
  gap: 3px;
  padding: 0 5px;
}

.icon-accounting .button {
  width: 10px;
  height: 8px;
  background-color: var(--white);
  border: 1px solid var(--border-light);
  border-radius: 3px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  animation: pressButtonAccountingImpact var(--medium-duration) infinite ease-in-out;
}

.icon-accounting .button:nth-child(4n+1) { animation-delay: 0.1s; }
.icon-accounting .button:nth-child(4n+2) { animation-delay: 0.4s; }
.icon-accounting .button:nth-child(4n+3) { animation-delay: 0.7s; }
.icon-accounting .button:nth-child(4n+4) { animation-delay: 1.0s; }

@keyframes pressButtonAccountingImpact {
  0%, 100% {
    transform: translateY(0) scale(1);
    background-color: var(--white);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  }
  5%, 15% {
    transform: translateY(1px) scale(0.95);
    background-color: var(--accent-pink);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15);
  }
  10% {
    transform: translateY(-0.5px) scale(1.02);
    background-color: var(--white);
    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.08);
  }
  20% {
    transform: translateY(0) scale(1);
    background-color: var(--white);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  }
}

/* --- Additional Maths - Engaging Animated Graph --- */
.icon-add-maths .graph-area {
  width: 60px;
  height: 60px;
  border-left: 2px solid var(--grey-medium);
  border-bottom: 2px solid var(--grey-medium);
  position: relative;
  background:
    linear-gradient(to right, var(--border-light) 1px, transparent 1px) 0 0 / 12px 12px,
    linear-gradient(to bottom, var(--border-light) 1px, transparent 1px) 0 0 / 12px 12px;
  border-radius: 0 0 0 4px;
}

.icon-add-maths svg {
  width: 100%;
  height: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  overflow: visible;
}

.icon-add-maths .curve path {
  stroke: var(--math-sci-blue);
  stroke-width: 2.5;
  fill: none;
  stroke-linecap: round;
  stroke-dasharray: 150;
  stroke-dashoffset: 150;
  animation: drawCurveEngage var(--medium-duration) infinite ease-in-out;
  filter: drop-shadow(0 1px 2px rgba(37, 99, 235, 0.3));
}

@keyframes drawCurveEngage {
  0% { stroke-dashoffset: 150; opacity: 0; }
  10% { opacity: 1; }
  50% { stroke-dashoffset: 0; opacity: 1; }
  60% { opacity: 1; }
  90% { opacity: 0; }
  100% { stroke-dashoffset: -150; opacity: 0; }
}

.icon-add-maths::before {
  content: 'y';
  position: absolute;
  top: 2px;
  left: 5px;
  font-size: 11px;
  font-weight: 500;
  color: var(--grey-dark);
}

.icon-add-maths::after {
  content: 'x';
  position: absolute;
  bottom: 2px;
  right: 5px;
  font-size: 11px;
  font-weight: 500;
  color: var(--grey-dark);
}

/* --- Biology - Enhanced Dynamic DNA Helix --- */
.icon-biology .dna-container {
  width: 40px;
  height: 65px;
  position: relative;
  perspective: 600px;
  transform-style: preserve-3d;
}

.icon-biology .strand-group {
  width: 100%;
  height: 100%;
  position: absolute;
  animation: rotateDNADynamic var(--slow-duration) infinite linear;
  transform-style: preserve-3d;
}

.icon-biology .strand {
  position: absolute;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  top: 50%;
  margin-top: -3px;
  transform-origin: center center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.icon-biology .strand.s1 {
  transform: rotateY(0deg) translateZ(14px);
  background: linear-gradient(135deg, var(--dark-slate), var(--prussian-blue));
}

.icon-biology .strand.s2 {
  transform: rotateY(180deg) translateZ(14px);
  background: linear-gradient(135deg, var(--bone), var(--gray-blue));
}

.icon-biology .pair {
  position: absolute;
  width: 22px;
  height: 4px;
  border-radius: 2px;
  left: calc(50% - 11px);
  transform-origin: center center;
  transform: rotateY(90deg) translateZ(0px);
  animation: twistPairDynamic var(--slow-duration) infinite linear;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.icon-biology .pair:nth-child(1) {
  top: 10%;
  animation-delay: 0s;
  background: linear-gradient(90deg, var(--burnt-sienna), var(--cinnabar));
}

.icon-biology .pair:nth-child(2) {
  top: 30%;
  animation-delay: -0.5s;
  background: linear-gradient(90deg, var(--prussian-blue), var(--gray-blue));
}

.icon-biology .pair:nth-child(3) {
  top: 50%;
  animation-delay: -1.0s;
  background: linear-gradient(90deg, var(--burnt-sienna), var(--cinnabar));
}

.icon-biology .pair:nth-child(4) {
  top: 70%;
  animation-delay: -1.5s;
  background: linear-gradient(90deg, var(--prussian-blue), var(--gray-blue));
}

.icon-biology .pair:nth-child(5) {
  top: 90%;
  animation-delay: -2.0s;
  background: linear-gradient(90deg, var(--burnt-sienna), var(--cinnabar));
}

/* Add nucleotide dots at the ends of pairs */
.icon-biology .pair::before,
.icon-biology .pair::after {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  top: -1px;
  background-color: var(--white);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.icon-biology .pair::before {
  left: -3px;
}

.icon-biology .pair::after {
  right: -3px;
}

.icon-biology .pair:nth-child(odd)::before {
  background-color: var(--maize);
}

.icon-biology .pair:nth-child(odd)::after {
  background-color: var(--orange-peel);
}

.icon-biology .pair:nth-child(even)::before {
  background-color: var(--prussian-blue);
}

.icon-biology .pair:nth-child(even)::after {
  background-color: var(--gray-blue);
}

/* Add glow effect */
.icon-biology .glow {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
  animation: pulseGlow 3s infinite alternate ease-in-out;
  pointer-events: none;
}

@keyframes pulseGlow {
  0% { opacity: 0.3; transform: scale(0.8); }
  100% { opacity: 0.7; transform: scale(1.2); }
}

@keyframes rotateDNADynamic {
  0% { transform: rotateY(0deg) rotateX(10deg); }
  50% { transform: rotateY(180deg) rotateX(-10deg); }
  100% { transform: rotateY(360deg) rotateX(10deg); }
}

@keyframes twistPairDynamic {
  0% { transform: rotateY(90deg) translateZ(0px) scale(1); opacity: 0.9; }
  50% { transform: rotateY(270deg) translateZ(0px) scale(1.05); opacity: 1; }
  100% { transform: rotateY(450deg) translateZ(0px) scale(1); opacity: 0.9; }
}

/* --- Business Studies - Impactful Bar Chart --- */
.icon-business .chart-container {
  width: 65px;
  height: 55px;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  border-bottom: 2px solid var(--grey-medium);
  position: relative;
  padding: 0 6px;
}

.icon-business .bar {
  width: 13px;
  background: linear-gradient(to top, var(--business-econ-orange), #FBBF24);
  border-radius: 4px 4px 0 0;
  animation: growBarImpact var(--medium-duration) infinite ease-out;
  position: relative;
  box-shadow: inset 0 -3px 5px rgba(0, 0, 0, 0.08);
  transform-origin: bottom center;
}

.icon-business .bar:nth-child(1) {
  height: 30px;
  animation-delay: 0s;
}

.icon-business .bar:nth-child(2) {
  height: 50px;
  animation-delay: 0.15s;
  background: linear-gradient(to top, var(--business-econ-orange-light), #FEF9C3);
}

.icon-business .bar:nth-child(3) {
  height: 40px;
  animation-delay: 0.3s;
}

.icon-business .bar:nth-child(4) {
  height: 25px;
  animation-delay: 0.45s;
  background: linear-gradient(to top, var(--business-econ-orange-light), #FEF9C3);
}

@keyframes growBarImpact {
  0% { transform: scaleY(0); opacity: 0; }
  60% { transform: scaleY(1.1); opacity: 1; }
  80% { transform: scaleY(0.95); }
  100% { transform: scaleY(1); opacity: 1; }
}

/* --- Chemistry - Lively Beaker --- */
.icon-chemistry .beaker-container {
  width: 50px;
  height: 60px;
  position: relative;
  filter: drop-shadow(0 3px 5px rgba(0, 0, 0, 0.07));
}

.icon-chemistry .beaker-glass {
  width: 100%;
  height: 100%;
  border: 2.5px solid var(--grey-medium);
  border-top: none;
  border-radius: 0 0 25px 25px;
  position: absolute;
  bottom: 0;
  background: linear-gradient(to top, var(--bio-chem-green-light) 10%, rgba(255, 255, 255, 0.5) 90%);
  overflow: hidden;
  z-index: 1;
  box-shadow: inset -3px -3px 6px rgba(0, 0, 0, 0.06);
}

.icon-chemistry .beaker-top {
  width: 112%;
  height: 10px;
  border: 2.5px solid var(--grey-medium);
  border-radius: 6px 6px 0 0;
  position: absolute;
  top: -6px;
  left: -6%;
  background: var(--white);
  z-index: 2;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.05);
}

.icon-chemistry .liquid-surface {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 70%;
  overflow: hidden;
  border-radius: 0 0 22px 22px;
  background-color: var(--bio-chem-green);
}

.icon-chemistry .liquid-surface::after {
  content: '';
  position: absolute;
  width: 180%;
  height: 12px;
  background: var(--bio-chem-green-light);
  border-radius: 45% 55% 40% 60%;
  top: -4px;
  left: -40%;
  animation: waveSurfaceLively var(--medium-duration) infinite linear alternate;
  opacity: 0.8;
  filter: blur(1px);
}

@keyframes waveSurfaceLively {
  0% { transform: translateX(0) rotate(-1deg); border-radius: 45% 55% 40% 60%; }
  100% { transform: translateX(-10px) rotate(1deg); border-radius: 55% 45% 60% 40%; }
}

.icon-chemistry .bubble {
  position: absolute;
  bottom: 8px;
  width: 6px;
  height: 6px;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 50%;
  animation: bubbleUpLively var(--fast-duration) infinite ease-in;
  z-index: 3;
  filter: blur(0.5px);
  opacity: 0;
}

.icon-chemistry .bubble:nth-child(1) {
  left: 25%;
  animation-delay: 0s;
  width: 5px;
  height: 5px;
}

.icon-chemistry .bubble:nth-child(2) {
  left: 70%;
  animation-delay: 0.6s;
  width: 7px;
  height: 7px;
}

.icon-chemistry .bubble:nth-child(3) {
  left: 45%;
  animation-delay: 1.2s;
  width: 6px;
  height: 6px;
}

.icon-chemistry .bubble:nth-child(4) {
  left: 55%;
  animation-delay: 1.8s;
  width: 4px;
  height: 4px;
}

@keyframes bubbleUpLively {
  0% { bottom: 8px; opacity: 0; transform: scale(0.4) translateX(0); }
  20% { opacity: 0.9; transform: scale(0.8); }
  80% { opacity: 0.2; transform: scale(1.1) translateX(3px); }
  95% { opacity: 0.1; transform: scale(1.15) translateX(-2px); }
  100% { bottom: 75%; opacity: 0; transform: scale(1.2) translateX(0); }
}

/* --- Computer Science - Realistic Terminal --- */
.icon-cs .terminal-window {
  width: 65px;
  height: 50px;
  background-color: #1a202c;
  border-radius: 8px;
  padding: 10px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border: 1px solid #2d3748;
  transform-origin: center;
  animation: terminalPulse 4s infinite ease-in-out;
}

@keyframes terminalPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
  }
}

.icon-cs .window-buttons {
  position: absolute;
  top: 7px;
  left: 10px;
  display: flex;
  gap: 6px;
}

.icon-cs .window-button {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.icon-cs .btn-red {
  background-color: #ff5f57;
}

.icon-cs .btn-yellow {
  background-color: #ffbd2e;
}

.icon-cs .btn-green {
  background-color: #28c940;
}

.icon-cs .code-area {
  position: absolute;
  top: 22px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  font-family: 'Menlo', 'Courier New', Courier, monospace;
  font-size: 8px;
  color: #A0AEC0;
  line-height: 1.4;
  overflow: hidden;
}

.icon-cs .code-line {
  white-space: pre;
  position: relative;
  height: 1.4em;
  color: #E2E8F0;
}

.icon-cs .code-line span {
  opacity: 0;
  animation: typeChar 0.1s forwards;
  color: inherit;
}

.icon-cs .code-line:nth-child(1) {
  animation-delay: 0.5s;
  color: #68D391;
}

.icon-cs .code-line:nth-child(2) {
  animation-delay: 2s;
  color: #4299E1;
}

.icon-cs .code-line:nth-child(3) {
  animation-delay: 3.5s;
  color: #F687B3;
}

.icon-cs .code-line:nth-child(1) span {
  --char-index: 0;
  animation-delay: calc(0.5s + (var(--char-index) * 0.05s));
}

.icon-cs .code-line:nth-child(2) span {
  --char-index: 0;
  animation-delay: calc(2s + (var(--char-index) * 0.05s));
}

.icon-cs .code-line:nth-child(3) span {
  --char-index: 0;
  animation-delay: calc(3.5s + (var(--char-index) * 0.05s));
}

@keyframes typeChar {
  from {
    opacity: 0;
    transform: translateY(1px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.icon-cs .cursor {
  width: 5px;
  height: 12px;
  background-color: #48BB78;
  position: absolute;
  top: 1px;
  animation: blink 1s infinite steps(2, start);
}

@keyframes blink {
  to {
    visibility: hidden;
  }
}

/* --- Economics - Enhanced Dynamic Dollar --- */
.icon-economics .chart-container {
  width: 60px;
  height: 60px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
}

.icon-economics .dollar-symbol {
  position: relative;
  font-size: 40px;
  font-weight: bold;
  color: var(--business-econ-orange);
  text-shadow:
    0 0 10px rgba(245, 158, 11, 0.6),
    0 0 20px rgba(245, 158, 11, 0.4);
  animation: pulseDollarEnhanced var(--medium-duration) infinite ease-in-out;
  z-index: 2;
}

.icon-economics .dollar-symbol::before {
  content: '$';
}

.icon-economics .glow-circle {
  position: absolute;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(254, 243, 199, 0.7) 0%, rgba(254, 243, 199, 0.1) 50%, transparent 70%);
  animation: pulseGlowEnhanced var(--medium-duration) infinite alternate;
  z-index: 1;
}

@keyframes pulseDollarEnhanced {
  0% {
    transform: scale(1);
    color: var(--business-econ-orange);
    text-shadow: 0 0 10px rgba(245, 158, 11, 0.6), 0 0 20px rgba(245, 158, 11, 0.4);
  }
  50% {
    transform: scale(1.12);
    color: #FBBF24;
    text-shadow: 0 0 18px rgba(245, 158, 11, 0.8), 0 0 35px rgba(245, 158, 11, 0.6);
  }
  100% {
    transform: scale(1);
    color: var(--business-econ-orange);
    text-shadow: 0 0 10px rgba(245, 158, 11, 0.6), 0 0 20px rgba(245, 158, 11, 0.4);
  }
}

@keyframes pulseGlowEnhanced {
  0% {
    transform: scale(0.9);
    opacity: 0.4;
  }
  100% {
    transform: scale(1.15);
    opacity: 0.7;
  }
}

.icon-economics .coin {
  position: absolute;
  width: 14px;
  height: 14px;
  background: linear-gradient(135deg, #FCD34D, #FBBF24 40%, #D97706);
  border-radius: 50%;
  border: 1.5px solid #92400E;
  box-shadow:
    inset 0 -1px 2px rgba(0, 0, 0, 0.25),
    inset 0 1px 1px rgba(255, 255, 255, 0.3),
    0 3px 5px rgba(0, 0, 0, 0.2),
    0 0 8px rgba(245, 158, 11, 0.5);
  opacity: 0;
  animation: floatCoinEnhanced var(--medium-duration) infinite ease-out;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  font-weight: bold;
  color: #92400E;
  z-index: 3;
}

.icon-economics .coin::before {
  content: '$';
  display: block;
  animation: inherit;
  animation-name: rotateCoinSymbol;
}

@keyframes floatCoinEnhanced {
  0% {
    transform: translateY(25px) rotateY(0deg) scale(0.4) rotateZ(0deg);
    opacity: 0;
  }
  15% {
    opacity: 0.9;
    transform: translateY(10px) rotateY(180deg) scale(1) rotateZ(45deg);
  }
  70% {
    opacity: 0.9;
    transform: translateY(-35px) rotateY(540deg) scale(1) rotateZ(270deg);
  }
  100% {
    transform: translateY(-55px) rotateY(720deg) scale(0.4) rotateZ(360deg);
    opacity: 0;
  }
}

@keyframes rotateCoinSymbol {
  0% {
    transform: rotateY(0deg) rotateZ(0deg);
  }
  15% {
    transform: rotateY(-180deg) rotateZ(-45deg);
  }
  70% {
    transform: rotateY(-540deg) rotateZ(-270deg);
  }
  100% {
    transform: rotateY(-720deg) rotateZ(-360deg);
  }
}

.icon-economics .coin1 {
  bottom: 5px;
  left: -10px;
  animation-delay: 0.2s;
}

.icon-economics .coin2 {
  bottom: 15px;
  right: -10px;
  animation-delay: 0.8s;
  width: 12px;
  height: 12px;
  font-size: 7px;
}

.icon-economics .coin3 {
  bottom: 30px;
  left: 5px;
  animation-delay: 1.4s;
  width: 10px;
  height: 10px;
  font-size: 6px;
}

.icon-economics .coin4 {
  bottom: 25px;
  right: 5px;
  animation-delay: 2.0s;
  width: 11px;
  height: 11px;
  font-size: 6.5px;
}

.icon-economics .coin5 {
  bottom: 5px;
  right: 15px;
  animation-delay: 0.5s;
  width: 13px;
  height: 13px;
  font-size: 7.5px;
}

.icon-economics .coin6 {
  bottom: 40px;
  left: 20px;
  animation-delay: 1.1s;
  width: 9px;
  height: 9px;
  font-size: 5.5px;
}

.icon-economics .coin7 {
  bottom: 10px;
  left: 25px;
  animation-delay: 1.7s;
  width: 12px;
  height: 12px;
  font-size: 7px;
}
