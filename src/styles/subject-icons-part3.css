/* --- Computer Science - Realistic Terminal --- */
.icon-cs .terminal-window {
  width: 65px;
  height: 50px;
  background-color: #1a202c;
  border-radius: 8px;
  padding: 10px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border: 1px solid #2d3748;
  transform-origin: center;
  animation: terminalPulse 4s infinite ease-in-out;
}

@keyframes terminalPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
  }
}

.icon-cs .window-buttons {
  position: absolute;
  top: 7px;
  left: 10px;
  display: flex;
  gap: 6px;
}

.icon-cs .window-button {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.icon-cs .btn-red {
  background-color: #ff5f57;
}

.icon-cs .btn-yellow {
  background-color: #ffbd2e;
}

.icon-cs .btn-green {
  background-color: #28c940;
}

.icon-cs .code-area {
  position: absolute;
  top: 22px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  font-family: 'Menlo', 'Courier New', Courier, monospace;
  font-size: 8px;
  color: #A0AEC0;
  line-height: 1.4;
  overflow: hidden;
}

.icon-cs .code-line {
  white-space: pre;
  position: relative;
  height: 1.4em;
  color: #E2E8F0;
}

.icon-cs .code-line span {
  opacity: 0;
  animation: typeChar 0.1s forwards;
  color: inherit;
}

.icon-cs .code-line:nth-child(1) {
  animation-delay: 0.5s;
  color: #68D391;
}

.icon-cs .code-line:nth-child(2) {
  animation-delay: 2s;
  color: #4299E1;
}

.icon-cs .code-line:nth-child(3) {
  animation-delay: 3.5s;
  color: #F687B3;
}

.icon-cs .code-line:nth-child(1) span {
  --char-index: 0;
  animation-delay: calc(0.5s + (var(--char-index) * 0.05s));
}

.icon-cs .code-line:nth-child(2) span {
  --char-index: 0;
  animation-delay: calc(2s + (var(--char-index) * 0.05s));
}

.icon-cs .code-line:nth-child(3) span {
  --char-index: 0;
  animation-delay: calc(3.5s + (var(--char-index) * 0.05s));
}

@keyframes typeChar {
  from {
    opacity: 0;
    transform: translateY(1px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.icon-cs .cursor {
  width: 5px;
  height: 12px;
  background-color: #48BB78;
  position: absolute;
  top: 1px;
  animation: blink 1s infinite steps(2, start);
}

@keyframes blink {
  to {
    visibility: hidden;
  }
}

/* --- Economics - Enhanced Dynamic Dollar --- */
.icon-economics .chart-container {
  width: 60px;
  height: 60px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
}

.icon-economics .dollar-symbol {
  position: relative;
  font-size: 40px;
  font-weight: bold;
  color: var(--business-econ-orange);
  text-shadow:
    0 0 10px rgba(245, 158, 11, 0.6),
    0 0 20px rgba(245, 158, 11, 0.4);
  animation: pulseDollarEnhanced var(--medium-duration) infinite ease-in-out;
  z-index: 2;
}

.icon-economics .dollar-symbol::before {
  content: '$';
}

.icon-economics .glow-circle {
  position: absolute;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(254, 243, 199, 0.7) 0%, rgba(254, 243, 199, 0.1) 50%, transparent 70%);
  animation: pulseGlowEnhanced var(--medium-duration) infinite alternate;
  z-index: 1;
}

@keyframes pulseDollarEnhanced {
  0% {
    transform: scale(1);
    color: var(--business-econ-orange);
    text-shadow: 0 0 10px rgba(245, 158, 11, 0.6), 0 0 20px rgba(245, 158, 11, 0.4);
  }
  50% {
    transform: scale(1.12);
    color: #FBBF24;
    text-shadow: 0 0 18px rgba(245, 158, 11, 0.8), 0 0 35px rgba(245, 158, 11, 0.6);
  }
  100% {
    transform: scale(1);
    color: var(--business-econ-orange);
    text-shadow: 0 0 10px rgba(245, 158, 11, 0.6), 0 0 20px rgba(245, 158, 11, 0.4);
  }
}

@keyframes pulseGlowEnhanced {
  0% {
    transform: scale(0.9);
    opacity: 0.4;
  }
  100% {
    transform: scale(1.15);
    opacity: 0.7;
  }
}

.icon-economics .coin {
  position: absolute;
  width: 14px;
  height: 14px;
  background: linear-gradient(135deg, #FCD34D, #FBBF24 40%, #D97706);
  border-radius: 50%;
  border: 1.5px solid #92400E;
  box-shadow:
    inset 0 -1px 2px rgba(0, 0, 0, 0.25),
    inset 0 1px 1px rgba(255, 255, 255, 0.3),
    0 3px 5px rgba(0, 0, 0, 0.2),
    0 0 8px rgba(245, 158, 11, 0.5);
  opacity: 0;
  animation: floatCoinEnhanced var(--medium-duration) infinite ease-out;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  font-weight: bold;
  color: #92400E;
  z-index: 3;
}

.icon-economics .coin::before {
  content: '$';
  display: block;
  animation: inherit;
  animation-name: rotateCoinSymbol;
}

@keyframes floatCoinEnhanced {
  0% {
    transform: translateY(25px) rotateY(0deg) scale(0.4) rotateZ(0deg);
    opacity: 0;
  }
  15% {
    opacity: 0.9;
    transform: translateY(10px) rotateY(180deg) scale(1) rotateZ(45deg);
  }
  70% {
    opacity: 0.9;
    transform: translateY(-35px) rotateY(540deg) scale(1) rotateZ(270deg);
  }
  100% {
    transform: translateY(-55px) rotateY(720deg) scale(0.4) rotateZ(360deg);
    opacity: 0;
  }
}

@keyframes rotateCoinSymbol {
  0% {
    transform: rotateY(0deg) rotateZ(0deg);
  }
  15% {
    transform: rotateY(-180deg) rotateZ(-45deg);
  }
  70% {
    transform: rotateY(-540deg) rotateZ(-270deg);
  }
  100% {
    transform: rotateY(-720deg) rotateZ(-360deg);
  }
}

.icon-economics .coin1 {
  bottom: 5px;
  left: -10px;
  animation-delay: 0.2s;
}

.icon-economics .coin2 {
  bottom: 15px;
  right: -10px;
  animation-delay: 0.8s;
  width: 12px;
  height: 12px;
  font-size: 7px;
}

.icon-economics .coin3 {
  bottom: 30px;
  left: 5px;
  animation-delay: 1.4s;
  width: 10px;
  height: 10px;
  font-size: 6px;
}

.icon-economics .coin4 {
  bottom: 25px;
  right: 5px;
  animation-delay: 2.0s;
  width: 11px;
  height: 11px;
  font-size: 6.5px;
}

.icon-economics .coin5 {
  bottom: 5px;
  right: 15px;
  animation-delay: 0.5s;
  width: 13px;
  height: 13px;
  font-size: 7.5px;
}

.icon-economics .coin6 {
  bottom: 40px;
  left: 20px;
  animation-delay: 1.1s;
  width: 9px;
  height: 9px;
  font-size: 5.5px;
}

.icon-economics .coin7 {
  bottom: 10px;
  left: 25px;
  animation-delay: 1.7s;
  width: 12px;
  height: 12px;
  font-size: 7px;
}
