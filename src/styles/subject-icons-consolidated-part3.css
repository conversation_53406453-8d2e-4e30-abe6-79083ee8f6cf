/* --- Language - Interactive Linguistic Sphere --- */
.icon-language .language-container {
  width: 60px;
  height: 60px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 800px;
}

.icon-language .sphere {
  width: 40px;
  height: 40px;
  position: relative;
  transform-style: preserve-3d;
  /* Removed full rotation animation */
}

.icon-language .sphere-core {
  position: absolute;
  width: 30px;
  height: 30px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, var(--prussian-blue) 0%, var(--gray-blue) 100%);
  box-shadow: 
    inset 0 0 15px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(0, 48, 73, 0.2);
  z-index: 1;
}

.icon-language .orbit {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  left: 0;
  top: 0;
  transform-style: preserve-3d;
}

.icon-language .orbit-1 {
  transform: rotateX(15deg) rotateY(0deg);
  animation: pulseOrbit 4s infinite alternate;
}

.icon-language .orbit-2 {
  transform: rotateX(0deg) rotateY(15deg);
  animation: pulseOrbit 4s infinite alternate-reverse;
}

.icon-language .orbit-3 {
  transform: rotateX(10deg) rotateZ(10deg);
  animation: pulseOrbit 4s infinite alternate 1s;
}

/* Characters floating around the sphere */
.icon-language .character {
  position: absolute;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 0 4px rgba(0, 0, 0, 0.8), 0 0 8px rgba(0, 0, 0, 0.4);
  transform-style: preserve-3d;
  z-index: 2;
  animation: floatCharacter 8s infinite ease-in-out;
}

.icon-language .char-a {
  top: 0px;
  left: 50%;
  transform: translate(-50%, 0);
  color: #FFFFFF;
  background-color: var(--cinnabar);
  border-radius: 4px;
  padding: 0 3px;
  animation-delay: 0s;
}

.icon-language .char-b {
  bottom: 0px;
  left: 50%;
  transform: translate(-50%, 0);
  color: #000000;
  background-color: var(--maize);
  border-radius: 4px;
  padding: 0 3px;
  animation-delay: 1s;
}

.icon-language .char-c {
  left: 0px;
  top: 50%;
  transform: translate(0, -50%);
  color: #FFFFFF;
  background-color: var(--deep-red);
  border-radius: 4px;
  padding: 0 3px;
  animation-delay: 2s;
}

.icon-language .char-d {
  right: 0px;
  top: 50%;
  transform: translate(0, -50%);
  color: #FFFFFF;
  background-color: var(--prussian-blue);
  border-radius: 4px;
  padding: 0 3px;
  animation-delay: 3s;
}

.icon-language .char-e {
  top: 25%;
  right: 20%;
  color: #000000;
  background-color: var(--bone);
  border-radius: 4px;
  padding: 0 3px;
  animation-delay: 1.5s;
}

.icon-language .char-f {
  bottom: 25%;
  left: 20%;
  color: #FFFFFF;
  background-color: var(--burnt-sienna);
  border-radius: 4px;
  padding: 0 3px;
  animation-delay: 2.5s;
}

/* Glowing connection lines */
.icon-language .connection {
  position: absolute;
  background: linear-gradient(90deg, var(--cinnabar), transparent);
  height: 1px;
  transform-origin: left center;
  opacity: 0;
  z-index: 1;
  animation: showConnection 3s infinite;
}

.icon-language .connection-1 {
  width: 20px;
  top: 30%;
  left: 50%;
  transform: rotate(45deg);
  animation-delay: 0.5s;
}

.icon-language .connection-2 {
  width: 25px;
  top: 60%;
  left: 40%;
  transform: rotate(-30deg);
  animation-delay: 1.5s;
}

.icon-language .connection-3 {
  width: 18px;
  top: 45%;
  right: 40%;
  transform: rotate(150deg);
  animation-delay: 2.5s;
}

/* Floating symbols */
.icon-language .symbol {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 2px;
  background-color: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  font-weight: bold;
  color: var(--white);
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
  opacity: 0;
  animation: floatSymbol 8s infinite;
}

.icon-language .symbol-1 {
  top: 10%;
  left: 20%;
  animation-delay: 0.5s;
}

.icon-language .symbol-2 {
  top: 70%;
  right: 15%;
  animation-delay: 2s;
}

.icon-language .symbol-3 {
  top: 40%;
  left: 10%;
  animation-delay: 3.5s;
}

/* Glow effect */
.icon-language .glow {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle at center, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
  animation: pulseGlow 4s infinite alternate ease-in-out;
  pointer-events: none;
  z-index: 0;
}

/* Removed rotateSphere animation */

@keyframes pulseOrbit {
  0% { opacity: 0.3; border-color: rgba(255, 255, 255, 0.1); }
  100% { opacity: 0.8; border-color: rgba(255, 255, 255, 0.3); }
}

@keyframes floatCharacter {
  0%, 100% { opacity: 0.9; transform: translate(0, 0) scale(1); box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); }
  25% { opacity: 1; transform: translate(3px, -3px) scale(1.1); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4); }
  50% { opacity: 1; transform: translate(0, -5px) scale(1.2); box-shadow: 0 3px 5px rgba(0, 0, 0, 0.5); }
  75% { opacity: 1; transform: translate(-3px, -3px) scale(1.1); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4); }
}

@keyframes showConnection {
  0%, 100% { opacity: 0; transform-origin: left center; transform: scaleX(0.1); }
  10% { opacity: 0.3; transform: scaleX(0.2); }
  50% { opacity: 0.7; transform: scaleX(1); }
  90% { opacity: 0.3; transform: scaleX(0.2); }
}

@keyframes floatSymbol {
  0%, 100% { opacity: 0; transform: translateY(5px) scale(0.8); }
  25%, 75% { opacity: 0.8; transform: translateY(0) scale(1); }
}

@keyframes pulseGlow {
  0% { opacity: 0.3; transform: scale(0.8); }
  100% { opacity: 0.6; transform: scale(1.2); }
}

/* --- Literature - Magical Book Experience --- */
.icon-literature .book-container {
  width: 60px;
  height: 60px;
  position: relative;
  perspective: 1000px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-literature .book {
  width: 42px;
  height: 52px;
  position: relative;
  transform-style: preserve-3d;
  transform-origin: center center;
  animation: floatBook 6s infinite ease-in-out;
}

.icon-literature .book-spine {
  position: absolute;
  width: 8px;
  height: 100%;
  left: 0;
  background: linear-gradient(to right, var(--prussian-blue), var(--dark-slate));
  border-radius: 2px 0 0 2px;
  transform-origin: right center;
  transform: rotateY(0deg) translateZ(0px);
  box-shadow: 
    -1px 0 3px rgba(0, 0, 0, 0.3),
    inset -1px 0 2px rgba(255, 255, 255, 0.2);
  z-index: 3;
}

.icon-literature .book-cover {
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--prussian-blue), var(--cinnabar));
  border-radius: 2px 6px 6px 2px;
  box-shadow: 
    -2px 0 5px rgba(0, 0, 0, 0.2),
    0 4px 8px rgba(0, 0, 0, 0.2);
  transform-origin: left center;
  transform-style: preserve-3d;
  animation: openCover 8s infinite alternate ease-in-out;
  z-index: 2;
}

/* Book title */
.icon-literature .book-title {
  position: absolute;
  width: 70%;
  height: 12%;
  top: 15%;
  left: 15%;
  background: linear-gradient(90deg, var(--maize), var(--bone));
  border-radius: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  animation: glowTitle 4s infinite alternate;
}

/* Book author */
.icon-literature .book-author {
  position: absolute;
  width: 50%;
  height: 6%;
  top: 35%;
  left: 25%;
  background: linear-gradient(90deg, var(--bone), var(--maize));
  border-radius: 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  opacity: 0.9;
}

/* Book decoration */
.icon-literature .book-decoration {
  position: absolute;
  width: 30%;
  height: 30%;
  bottom: 15%;
  right: 15%;
  border-radius: 50%;
  background: radial-gradient(circle at center, var(--orange-peel) 0%, var(--burnt-sienna) 70%);
  opacity: 0.7;
  box-shadow: 0 0 10px rgba(247, 127, 0, 0.4);
  animation: pulseSeal 4s infinite alternate;
}

.icon-literature .book-page {
  position: absolute;
  width: 95%;
  height: 95%;
  top: 2.5%;
  left: 0;
  background-color: var(--bone);
  border-radius: 0 4px 4px 0;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
  transform-origin: left center;
  transform-style: preserve-3d;
  z-index: 1;
}

.icon-literature .book-page-1 {
  background: linear-gradient(to right, var(--bone) 0%, #FFFFFF 100%);
  animation: turnPage1 8s infinite ease-in-out;
}

.icon-literature .book-page-2 {
  background: linear-gradient(to right, var(--bone) 0%, #FFFFFF 100%);
  animation: turnPage2 8s infinite ease-in-out;
}

.icon-literature .book-page-3 {
  background: linear-gradient(to right, var(--bone) 0%, #FFFFFF 100%);
  animation: turnPage3 8s infinite ease-in-out;
}

.icon-literature .book-text {
  position: absolute;
  width: 80%;
  height: 70%;
  top: 15%;
  left: 15%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.icon-literature .text-line {
  height: 2px;
  background: linear-gradient(90deg, var(--prussian-blue), transparent);
  border-radius: 1px;
  opacity: 0;
  animation: fadeInLine 8s infinite;
}

.icon-literature .text-line:nth-child(1) {
  animation-delay: 2s;
}

.icon-literature .text-line:nth-child(2) {
  animation-delay: 2.2s;
}

.icon-literature .text-line:nth-child(3) {
  animation-delay: 2.4s;
}

.icon-literature .text-line:nth-child(4) {
  animation-delay: 2.6s;
}

/* Magical particles */
.icon-literature .particle {
  position: absolute;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background-color: var(--maize);
  opacity: 0;
  z-index: 4;
  animation: floatParticle 4s infinite;
}

.icon-literature .particle-1 {
  top: 20%;
  left: 60%;
  animation-delay: 0.5s;
}

.icon-literature .particle-2 {
  top: 50%;
  left: 70%;
  animation-delay: 1s;
}

.icon-literature .particle-3 {
  top: 70%;
  left: 50%;
  animation-delay: 1.5s;
}

.icon-literature .particle-4 {
  top: 30%;
  left: 80%;
  animation-delay: 2s;
}

.icon-literature .particle-5 {
  top: 60%;
  left: 90%;
  animation-delay: 2.5s;
}

/* Glow effect */
.icon-literature .glow {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle at center, rgba(252,191,73,0.2) 0%, rgba(255,255,255,0) 70%);
  animation: pulseGlow 4s infinite alternate ease-in-out;
  pointer-events: none;
  z-index: 0;
}

@keyframes floatBook {
  0%, 100% { transform: translateY(0) rotateX(0deg) rotateZ(0deg); }
  50% { transform: translateY(-3px) rotateX(5deg) rotateZ(1deg); }
}

@keyframes openCover {
  0%, 30% { transform: rotateY(0deg); }
  40%, 100% { transform: rotateY(-60deg); }
}

@keyframes turnPage1 {
  0%, 40% { transform: rotateY(0deg); opacity: 1; }
  50%, 100% { transform: rotateY(-140deg); opacity: 0.9; }
}

@keyframes turnPage2 {
  0%, 45% { transform: rotateY(0deg); opacity: 0.9; }
  55%, 100% { transform: rotateY(-140deg); opacity: 0.8; }
}

@keyframes turnPage3 {
  0%, 50% { transform: rotateY(0deg); opacity: 0.8; }
  60%, 100% { transform: rotateY(-140deg); opacity: 0.7; }
}

@keyframes fadeInLine {
  0%, 30% { opacity: 0; width: 0; }
  40%, 60% { opacity: 0.7; width: 100%; }
  90%, 100% { opacity: 0; width: 100%; }
}

@keyframes floatParticle {
  0% { opacity: 0; transform: translate(0, 0) scale(0.8); }
  20% { opacity: 1; transform: translate(5px, -10px) scale(1.2); }
  80% { opacity: 1; transform: translate(10px, -20px) scale(0.8); }
  100% { opacity: 0; transform: translate(15px, -30px) scale(0.4); }
}

@keyframes glowTitle {
  0%, 100% { box-shadow: 0 0 5px rgba(252, 191, 73, 0.5); }
  50% { box-shadow: 0 0 10px rgba(252, 191, 73, 0.8); }
}

@keyframes pulseSeal {
  0%, 100% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.1); opacity: 0.9; }
}

/* --- Mathematics - Pi and Orbiting Numbers --- */
.icon-mathematics .math-container {
  width: 60px;
  height: 60px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-mathematics .pi-symbol {
  font-size: 40px;
  font-weight: bold;
  color: var(--prussian-blue);
  position: relative;
  z-index: 1;
  text-shadow: 0 2px 5px rgba(0, 48, 73, 0.4);
  animation: pulsePiOrbiting var(--medium-duration) infinite alternate;
}

.icon-mathematics .pi-symbol::before {
  content: 'π';
}

.icon-mathematics .orbit {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 1px dashed var(--gray-blue);
  border-radius: 50%;
  animation: rotateOrbitOrbiting var(--slow-duration) infinite linear;
}

.icon-mathematics .orbit-1 {
  width: 90%;
  height: 90%;
  animation-duration: var(--slow-duration);
}

.icon-mathematics .orbit-2 {
  width: 70%;
  height: 70%;
  animation-duration: calc(var(--slow-duration) * 0.7);
  animation-direction: reverse;
}

.icon-mathematics .orbit-3 {
  width: 50%;
  height: 50%;
  animation-duration: calc(var(--slow-duration) * 0.5);
}

.icon-mathematics .number {
  position: absolute;
  width: 16px;
  height: 16px;
  background-color: var(--white);
  border: 1.5px solid var(--prussian-blue);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  color: var(--prussian-blue);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.icon-mathematics .number-1 {
  animation: orbitNumber1Orbiting var(--slow-duration) infinite linear;
}

.icon-mathematics .number-1::before {
  content: '3';
}

.icon-mathematics .number-2 {
  animation: orbitNumber2Orbiting calc(var(--slow-duration) * 0.7) infinite linear reverse;
}

.icon-mathematics .number-2::before {
  content: '1';
}

.icon-mathematics .number-3 {
  animation: orbitNumber3Orbiting calc(var(--slow-duration) * 0.5) infinite linear;
}

.icon-mathematics .number-3::before {
  content: '4';
}

@keyframes pulsePiOrbiting {
  0% {
    transform: scale(1);
    text-shadow: 0 2px 5px rgba(0, 48, 73, 0.4);
  }
  100% {
    transform: scale(1.1);
    text-shadow: 0 4px 10px rgba(37, 99, 235, 0.6);
  }
}

@keyframes rotateOrbitOrbiting {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes orbitNumber1Orbiting {
  0% {
    transform: rotate(0deg) translateX(30px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(30px) rotate(-360deg);
  }
}

@keyframes orbitNumber2Orbiting {
  0% {
    transform: rotate(0deg) translateX(23px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(23px) rotate(-360deg);
  }
}

@keyframes orbitNumber3Orbiting {
  0% {
    transform: rotate(0deg) translateX(16px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(16px) rotate(-360deg);
  }
}

/* --- Physics - Atomic Model --- */
.icon-physics .atom-container {
  width: 60px;
  height: 60px;
  position: relative;
  perspective: 800px;
}

.icon-physics .nucleus {
  width: 16px;
  height: 16px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -8px;
  margin-left: -8px;
  background: radial-gradient(circle at 30% 30%, var(--gray-blue) 0%, var(--prussian-blue) 70%, var(--dark-slate) 100%);
  border-radius: 50%;
  box-shadow: 0 0 15px rgba(37, 99, 235, 0.6);
  z-index: 3;
  animation: pulseNucleusAtomic var(--medium-duration) infinite alternate;
}

.icon-physics .electron-shell {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 50px;
  height: 50px;
  margin-top: -25px;
  margin-left: -25px;
  border-radius: 50%;
  border: 1px solid rgba(37, 99, 235, 0.3);
  transform-style: preserve-3d;
  animation: rotateShellAtomic var(--slow-duration) infinite linear;
}

.icon-physics .shell-1 {
  transform: rotateX(60deg) rotateY(0deg);
}

.icon-physics .shell-2 {
  transform: rotateX(0deg) rotateY(60deg);
  width: 40px;
  height: 40px;
  margin-top: -20px;
  margin-left: -20px;
  animation-direction: reverse;
  animation-duration: calc(var(--slow-duration) * 0.8);
}

.icon-physics .shell-3 {
  transform: rotateX(60deg) rotateY(60deg);
  width: 30px;
  height: 30px;
  margin-top: -15px;
  margin-left: -15px;
  animation-duration: calc(var(--slow-duration) * 0.6);
}

.icon-physics .electron {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: var(--math-sci-blue-light);
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(37, 99, 235, 0.5);
  z-index: 2;
}

.icon-physics .electron-1 {
  top: 50%;
  left: 0;
  margin-top: -4px;
  animation: moveElectron1Atomic var(--slow-duration) infinite linear;
}

.icon-physics .electron-2 {
  top: 0;
  left: 50%;
  margin-left: -4px;
  animation: moveElectron2Atomic calc(var(--slow-duration) * 0.8) infinite linear reverse;
}

.icon-physics .electron-3 {
  top: 50%;
  right: 0;
  margin-top: -4px;
  animation: moveElectron3Atomic calc(var(--slow-duration) * 0.6) infinite linear;
}

@keyframes pulseNucleusAtomic {
  0% {
    transform: scale(1);
    box-shadow: 0 0 15px rgba(37, 99, 235, 0.6);
  }
  100% {
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.8);
  }
}

@keyframes rotateShellAtomic {
  0% {
    transform: rotateX(60deg) rotateY(0deg);
  }
  100% {
    transform: rotateX(60deg) rotateY(360deg);
  }
}

@keyframes moveElectron1Atomic {
  0% {
    transform: rotate(0deg) translateX(25px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(25px) rotate(-360deg);
  }
}

@keyframes moveElectron2Atomic {
  0% {
    transform: rotate(0deg) translateX(20px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(20px) rotate(-360deg);
  }
}

@keyframes moveElectron3Atomic {
  0% {
    transform: rotate(0deg) translateX(15px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(15px) rotate(-360deg);
  }
}
