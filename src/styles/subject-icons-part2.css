/* --- Biology - Dynamic DNA Helix --- */
.icon-biology .dna-container {
  width: 35px;
  height: 65px;
  position: relative;
  perspective: 500px;
}

.icon-biology .strand-group {
  width: 100%;
  height: 100%;
  position: absolute;
  animation: rotateDNADynamic var(--slow-duration) infinite linear;
  transform-style: preserve-3d;
}

.icon-biology .strand {
  position: absolute;
  width: 100%;
  height: 7px;
  border-radius: 3.5px;
  top: 50%;
  margin-top: -3.5px;
  transform-origin: center center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.icon-biology .strand.s1 {
  transform: rotateY(0deg) translateZ(14px);
  background: linear-gradient(135deg, var(--bio-chem-green), #047857);
}

.icon-biology .strand.s2 {
  transform: rotateY(180deg) translateZ(14px);
  background: linear-gradient(135deg, var(--bio-chem-green-light), #6EE7B7);
}

.icon-biology .pair {
  position: absolute;
  width: 22px;
  height: 4px;
  border-radius: 2px;
  left: calc(50% - 11px);
  transform-origin: center center;
  transform: rotateY(90deg) translateZ(0px);
  animation: twistPairDynamic var(--slow-duration) infinite linear;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.icon-biology .pair:nth-child(1) {
  top: 10%;
  animation-delay: 0s;
  background: linear-gradient(90deg, var(--accent-pink), #F472B6);
}

.icon-biology .pair:nth-child(2) {
  top: 30%;
  animation-delay: -0.5s;
  background: linear-gradient(90deg, var(--math-sci-blue), #60A5FA);
}

.icon-biology .pair:nth-child(3) {
  top: 50%;
  animation-delay: -1.0s;
  background: linear-gradient(90deg, var(--accent-pink), #F472B6);
}

.icon-biology .pair:nth-child(4) {
  top: 70%;
  animation-delay: -1.5s;
  background: linear-gradient(90deg, var(--math-sci-blue), #60A5FA);
}

.icon-biology .pair:nth-child(5) {
  top: 90%;
  animation-delay: -2.0s;
  background: linear-gradient(90deg, var(--accent-pink), #F472B6);
}

@keyframes rotateDNADynamic {
  0% { transform: rotateY(0deg) rotateX(8deg); }
  50% { transform: rotateY(180deg) rotateX(-8deg); }
  100% { transform: rotateY(360deg) rotateX(8deg); }
}

@keyframes twistPairDynamic {
  0% { transform: rotateY(90deg) translateZ(0px) scale(1); opacity: 0.9; }
  50% { transform: rotateY(270deg) translateZ(0px) scale(1.05); opacity: 1; }
  100% { transform: rotateY(450deg) translateZ(0px) scale(1); opacity: 0.9; }
}

/* --- Business Studies - Impactful Bar Chart --- */
.icon-business .chart-container {
  width: 65px;
  height: 55px;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  border-bottom: 2px solid var(--grey-medium);
  position: relative;
  padding: 0 6px;
}

.icon-business .bar {
  width: 13px;
  background: linear-gradient(to top, var(--business-econ-orange), #FBBF24);
  border-radius: 4px 4px 0 0;
  animation: growBarImpact var(--medium-duration) infinite ease-out;
  position: relative;
  box-shadow: inset 0 -3px 5px rgba(0, 0, 0, 0.08);
  transform-origin: bottom center;
}

.icon-business .bar:nth-child(1) {
  height: 30px;
  animation-delay: 0s;
}

.icon-business .bar:nth-child(2) {
  height: 50px;
  animation-delay: 0.15s;
  background: linear-gradient(to top, var(--business-econ-orange-light), #FEF9C3);
}

.icon-business .bar:nth-child(3) {
  height: 40px;
  animation-delay: 0.3s;
}

.icon-business .bar:nth-child(4) {
  height: 25px;
  animation-delay: 0.45s;
  background: linear-gradient(to top, var(--business-econ-orange-light), #FEF9C3);
}

@keyframes growBarImpact {
  0% { transform: scaleY(0); opacity: 0; }
  60% { transform: scaleY(1.1); opacity: 1; }
  80% { transform: scaleY(0.95); }
  100% { transform: scaleY(1); opacity: 1; }
}

/* --- Chemistry - Lively Beaker --- */
.icon-chemistry .beaker-container {
  width: 50px;
  height: 60px;
  position: relative;
  filter: drop-shadow(0 3px 5px rgba(0, 0, 0, 0.07));
}

.icon-chemistry .beaker-glass {
  width: 100%;
  height: 100%;
  border: 2.5px solid var(--grey-medium);
  border-top: none;
  border-radius: 0 0 25px 25px;
  position: absolute;
  bottom: 0;
  background: linear-gradient(to top, var(--bio-chem-green-light) 10%, rgba(255, 255, 255, 0.5) 90%);
  overflow: hidden;
  z-index: 1;
  box-shadow: inset -3px -3px 6px rgba(0, 0, 0, 0.06);
}

.icon-chemistry .beaker-top {
  width: 112%;
  height: 10px;
  border: 2.5px solid var(--grey-medium);
  border-radius: 6px 6px 0 0;
  position: absolute;
  top: -6px;
  left: -6%;
  background: var(--white);
  z-index: 2;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.05);
}

.icon-chemistry .liquid-surface {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 70%;
  overflow: hidden;
  border-radius: 0 0 22px 22px;
  background-color: var(--bio-chem-green);
}

.icon-chemistry .liquid-surface::after {
  content: '';
  position: absolute;
  width: 180%;
  height: 12px;
  background: var(--bio-chem-green-light);
  border-radius: 45% 55% 40% 60%;
  top: -4px;
  left: -40%;
  animation: waveSurfaceLively var(--medium-duration) infinite linear alternate;
  opacity: 0.8;
  filter: blur(1px);
}

@keyframes waveSurfaceLively {
  0% { transform: translateX(0) rotate(-1deg); border-radius: 45% 55% 40% 60%; }
  100% { transform: translateX(-10px) rotate(1deg); border-radius: 55% 45% 60% 40%; }
}

.icon-chemistry .bubble {
  position: absolute;
  bottom: 8px;
  width: 6px;
  height: 6px;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 50%;
  animation: bubbleUpLively var(--fast-duration) infinite ease-in;
  z-index: 3;
  filter: blur(0.5px);
  opacity: 0;
}

.icon-chemistry .bubble:nth-child(1) {
  left: 25%;
  animation-delay: 0s;
  width: 5px;
  height: 5px;
}

.icon-chemistry .bubble:nth-child(2) {
  left: 70%;
  animation-delay: 0.6s;
  width: 7px;
  height: 7px;
}

.icon-chemistry .bubble:nth-child(3) {
  left: 45%;
  animation-delay: 1.2s;
  width: 6px;
  height: 6px;
}

.icon-chemistry .bubble:nth-child(4) {
  left: 55%;
  animation-delay: 1.8s;
  width: 4px;
  height: 4px;
}

@keyframes bubbleUpLively {
  0% { bottom: 8px; opacity: 0; transform: scale(0.4) translateX(0); }
  20% { opacity: 0.9; transform: scale(0.8); }
  80% { opacity: 0.2; transform: scale(1.1) translateX(3px); }
  95% { opacity: 0.1; transform: scale(1.15) translateX(-2px); }
  100% { bottom: 75%; opacity: 0; transform: scale(1.2) translateX(0); }
}
