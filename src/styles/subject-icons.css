/* --- Base Variables and Styles --- */
:root {
  --night: #151616ff;
  --white: #FEFEFEff;
  --celeste: #A0EBEBff;
  --night-2: #0F0F0Fff;
  --white-2: #FFFFFFff;
  --white: #FFFFFF;

  /* Subject Group Palettes - Adjusted for Professional Look */
  --math-sci-blue: #2563EB;
  --math-sci-blue-light: #BFDBFE;
  --bio-chem-green: #059669;
  --bio-chem-green-light: #A7F3D0;
  --business-econ-orange: #F59E0B;
  --business-econ-orange-light: #FEF3C7;
  --humanities-purple: #7C3AED;
  --humanities-purple-light: #DDD6FE;
  --accent-pink: #EC4899;
  --accent-red: #EF4444;

  /* Professional Greys */
  --grey-light: #F3F4F6;
  --grey-medium: #9CA3AF;
  --grey-dark: #4B5563;
  --border-light: #E5E7EB;
  --text-dark: #374151;

  /* Animation Durations */
  --fast-duration: 2.5s;
  --medium-duration: 4s;
  --slow-duration: 6s;
  --very-slow-duration: 10s;
}

/* --- Common Icon Wrapper --- */
.icon-wrapper {
  width: 90px;
  height: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  font-family: 'Inter', sans-serif;
  background-color: var(--white);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid var(--border-light);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.04);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.icon-wrapper:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08), 0 2px 6px rgba(0, 0, 0, 0.05);
}

/* --- Accounting - Dynamic Calculator --- */
.icon-accounting .calculator-body {
  width: 50px;
  height: 60px;
  background: linear-gradient(145deg, #f9fafb, #e5e7eb);
  border-radius: 8px;
  position: relative;
  border: 1px solid var(--border-light);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.06), 0 3px 6px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 6px;
}

.icon-accounting .screen {
  width: 40px;
  height: 14px;
  background-color: #D1FAE5;
  border-radius: 4px;
  margin-bottom: 6px;
  font-size: 9px;
  font-family: 'Courier New', Courier, monospace;
  color: var(--text-dark);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 4px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.icon-accounting .screen::after {
  content: '1,234';
  position: absolute;
  right: 4px;
  animation: calcNumbersDynamic var(--slow-duration) infinite steps(8, end);
}

@keyframes calcNumbersDynamic {
  0% { content: '1,234'; }
  12% { content: '+ 567'; }
  24% { content: '= 1,801'; }
  36% { content: '/ 7'; }
  48% { content: '= 257.28'; }
  60% { content: '* 10'; }
  72% { content: '= 2,572'; }
  84% { content: 'TOTAL'; }
  100% { content: '2,572'; }
}

.icon-accounting .buttons {
  display: grid;
  grid-template-columns: repeat(4, 10px);
  gap: 3px;
  padding: 0 5px;
}

.icon-accounting .button {
  width: 10px;
  height: 8px;
  background-color: var(--white);
  border: 1px solid var(--border-light);
  border-radius: 3px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  animation: pressButtonAccountingImpact var(--medium-duration) infinite ease-in-out;
}

.icon-accounting .button:nth-child(4n+1) { animation-delay: 0.1s; }
.icon-accounting .button:nth-child(4n+2) { animation-delay: 0.4s; }
.icon-accounting .button:nth-child(4n+3) { animation-delay: 0.7s; }
.icon-accounting .button:nth-child(4n+4) { animation-delay: 1.0s; }

@keyframes pressButtonAccountingImpact {
  0%, 100% {
    transform: translateY(0) scale(1);
    background-color: var(--white);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  }
  5%, 15% {
    transform: translateY(1px) scale(0.95);
    background-color: var(--accent-pink);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15);
  }
  10% {
    transform: translateY(-0.5px) scale(1.02);
    background-color: var(--white);
    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.08);
  }
  20% {
    transform: translateY(0) scale(1);
    background-color: var(--white);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  }
}

/* --- Additional Maths - Engaging Animated Graph --- */
.icon-add-maths .graph-area {
  width: 60px;
  height: 60px;
  border-left: 2px solid var(--grey-medium);
  border-bottom: 2px solid var(--grey-medium);
  position: relative;
  background:
    linear-gradient(to right, var(--border-light) 1px, transparent 1px) 0 0 / 12px 12px,
    linear-gradient(to bottom, var(--border-light) 1px, transparent 1px) 0 0 / 12px 12px;
  border-radius: 0 0 0 4px;
}

.icon-add-maths svg {
  width: 100%;
  height: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  overflow: visible;
}

.icon-add-maths .curve path {
  stroke: var(--math-sci-blue);
  stroke-width: 2.5;
  fill: none;
  stroke-linecap: round;
  stroke-dasharray: 150;
  stroke-dashoffset: 150;
  animation: drawCurveEngage var(--medium-duration) infinite ease-in-out;
  filter: drop-shadow(0 1px 2px rgba(37, 99, 235, 0.3));
}

@keyframes drawCurveEngage {
  0% { stroke-dashoffset: 150; opacity: 0; }
  10% { opacity: 1; }
  50% { stroke-dashoffset: 0; opacity: 1; }
  60% { opacity: 1; }
  90% { opacity: 0; }
  100% { stroke-dashoffset: -150; opacity: 0; }
}

.icon-add-maths::before {
  content: 'y';
  position: absolute;
  top: 2px;
  left: 5px;
  font-size: 11px;
  font-weight: 500;
  color: var(--grey-dark);
}

.icon-add-maths::after {
  content: 'x';
  position: absolute;
  bottom: 2px;
  right: 5px;
  font-size: 11px;
  font-weight: 500;
  color: var(--grey-dark);
}

/* Import all the parts */
@import 'subject-icons-part2.css';
@import 'subject-icons-part3.css';
@import 'subject-icons-part4.css';
@import 'subject-icons-part5.css';
@import 'subject-icons-part6.css';
@import 'subject-icons-part7.css';
@import 'subject-icons-part8.css';
