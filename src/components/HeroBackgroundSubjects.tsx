import { useEffect, useRef } from 'react';
import '../styles/subject-icons-consolidated.css';
import '../styles/subject-icons-consolidated-part2.css';
import '../styles/subject-icons-consolidated-part3.css';
import '../styles/subject-icons-consolidated-part4.css';

// Subject icon configs: iconClass, color, label
const featuredSubjects = [
  { iconClass: 'icon-mathematics', label: 'Mathematics', orbit: 180, color: '#F77F00' },
  { iconClass: 'icon-biology', label: 'Biology', orbit: 60, color: '#D62828' },
  { iconClass: 'icon-chemistry', label: 'Chemistry', orbit: 300, color: '#FCBF49' },
  { iconClass: 'icon-physics', label: 'Physics', orbit: 240, color: '#003049' },
  { iconClass: 'icon-economics', label: 'Economics', orbit: 120, color: '#EAE2B7' },
  { iconClass: 'icon-business', label: 'Business', orbit: 0, color: '#F77F00' },
  { iconClass: 'icon-accounting', label: 'Accounting', orbit: 90, color: '#D62828' },
];

// Helper to position icons in a circle
function getOrbitPosition(angleDeg: number, radius: number, center: number) {
  const angleRad = (angleDeg * Math.PI) / 180;
  return {
    left: `${center + radius * Math.cos(angleRad)}%`,
    top: `${center + radius * Math.sin(angleRad)}%`,
  };
}

const HeroBackgroundSubjects = () => {
  const globeRef = useRef<HTMLDivElement>(null);

  // Animate globe rotation
  useEffect(() => {
    let frame: number;
    let angle = 0;
    const animate = () => {
      angle = (angle + 0.2) % 360;
      if (globeRef.current) {
        globeRef.current.style.transform = `rotate(${angle}deg)`;
      }
      frame = requestAnimationFrame(animate);
    };
    animate();
    return () => cancelAnimationFrame(frame);
  }, []);

  return (
    <div className="absolute inset-0 w-full h-full pointer-events-none -z-10 overflow-hidden">
      {/* Vibrant gradient background overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#F77F00]/40 via-[#FCBF49]/30 to-[#003049]/40 animate-gradient" />

      {/* Animated Globe (Geography icon) */}
      <div
        ref={globeRef}
        className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
        style={{ width: 420, height: 420, zIndex: 1 }}
      >
        <div className="icon-wrapper icon-geography w-full h-full opacity-90 scale-125" />
        {/* Glow */}
        <div className="absolute inset-0 rounded-full bg-[#FCBF49]/30 blur-2xl animate-pulse" style={{ zIndex: -1 }} />
      </div>

      {/* Subject Icons orbiting the globe */}
      {featuredSubjects.map((subject, i) => {
        // Animate each icon in a vibrant orbit
        const orbitRadius = 36; // percent from center
        const center = 50;
        const orbitDuration = 10 + i * 1.5;
        return (
          <div
            key={subject.iconClass}
            className="absolute"
            style={{
              ...getOrbitPosition(subject.orbit, orbitRadius, center),
              width: 90,
              height: 90,
              zIndex: 2,
              animation: `orbit${i} ${orbitDuration}s linear infinite`,
            }}
          >
            <div
              className={`icon-wrapper ${subject.iconClass} w-full h-full drop-shadow-xl flex items-center justify-center`}
              style={{ filter: `drop-shadow(0 0 16px ${subject.color})` }}
            >
              {/* Render correct icon markup */}
              {subject.iconClass === 'icon-mathematics' && (
                <div className="math-container">
                  <div className="pi-symbol"></div>
                  <div className="orbit orbit-1"></div>
                  <div className="orbit orbit-2"></div>
                  <div className="orbit orbit-3"></div>
                  <div className="number number-1"></div>
                  <div className="number number-2"></div>
                  <div className="number number-3"></div>
                </div>
              )}
              {subject.iconClass === 'icon-biology' && (
                <div className="dna-container">
                  <div className="strand-group">
                    <div className="strand s1"></div>
                    <div className="strand s2"></div>
                    {Array.from({ length: 5 }).map((_, i) => <div key={i} className="pair"></div>)}
                  </div>
                  <div className="glow"></div>
                </div>
              )}
              {subject.iconClass === 'icon-chemistry' && (
                <div className="beaker-container">
                  <div className="beaker-glass">
                    <div className="liquid-surface">
                      {Array.from({ length: 4 }).map((_, i) => <div key={i} className="bubble"></div>)}
                    </div>
                  </div>
                  <div className="beaker-top"></div>
                </div>
              )}
              {subject.iconClass === 'icon-physics' && (
                <div className="atom-container">
                  <div className="nucleus"></div>
                  <div className="electron-shell shell-1"></div>
                  <div className="electron-shell shell-2"></div>
                  <div className="electron-shell shell-3"></div>
                  <div className="electron electron-1"></div>
                  <div className="electron electron-2"></div>
                  <div className="electron electron-3"></div>
                </div>
              )}
              {subject.iconClass === 'icon-economics' && (
                <div className="chart-container">
                  <div className="dollar-symbol"></div>
                  <div className="glow-circle"></div>
                  {Array.from({ length: 7 }).map((_, i) => <div key={i} className={`coin coin${i + 1}`}></div>)}
                </div>
              )}
              {subject.iconClass === 'icon-business' && (
                <div className="chart-container">
                  {Array.from({ length: 4 }).map((_, i) => <div key={i} className="bar"></div>)}
                </div>
              )}
              {subject.iconClass === 'icon-accounting' && (
                <div className="calculator-body">
                  <div className="screen"></div>
                  <div className="buttons">
                    {Array.from({ length: 12 }).map((_, i) => <div key={i} className="button"></div>)}
                  </div>
                </div>
              )}
            </div>
            {/* Glow ring */}
            <div
              className="absolute inset-0 rounded-full blur-xl opacity-60 animate-pulse"
              style={{ background: `radial-gradient(circle, ${subject.color}55 0%, transparent 80%)` }}
            />
          </div>
        );
      })}

      {/* Connecting lines (SVG) */}
      <svg className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 pointer-events-none" width="420" height="420" style={{ zIndex: 0 }}>
        {featuredSubjects.map((subject, i) => {
          const angleRad = (subject.orbit * Math.PI) / 180;
          const r = 180;
          const x = 210 + r * Math.cos(angleRad);
          const y = 210 + r * Math.sin(angleRad);
          return (
            <line
              key={subject.iconClass}
              x1="210" y1="210" x2={x} y2={y}
              stroke={subject.color}
              strokeWidth="2"
              opacity="0.5"
              strokeDasharray="8 6"
            />
          );
        })}
      </svg>

      {/* Keyframes for orbits */}
      <style>{`
        ${featuredSubjects.map((subject, i) => `
          @keyframes orbit${i} {
            0% { transform: translate(-50%, -50%) rotate(${subject.orbit}deg) translate(${36}%, 0) rotate(-${subject.orbit}deg); }
            100% { transform: translate(-50%, -50%) rotate(${subject.orbit + 360}deg) translate(${36}%, 0) rotate(-${subject.orbit + 360}deg); }
          }
        `).join('')}
      `}</style>
    </div>
  );
};

export default HeroBackgroundSubjects; 