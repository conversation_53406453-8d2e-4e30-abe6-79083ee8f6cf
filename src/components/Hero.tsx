import { GraduationCap, Users, Globe2 } from 'lucide-react';
import { motion, useAnimation } from 'framer-motion';
import HeroButton from './HeroButton';
import { useEffect, useState, useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Grid } from '@react-three/drei';
import * as THREE from 'three';
import HeroBackgroundSubjects from './HeroBackgroundSubjects';

// Animation text content
const heroTexts = [
  {
    title: "Transform Your Learning Journey",
    subtitle: "With Expert Tutors Worldwide",
    emptyLine: "‎"
  },
  {
    title: "Personalized 1-on-1 Learning",
    subtitle: "Tailored to Your Academic Goals",
    emptyLine: "‎"
  },
  {
    title: "Expert IGCSE & IB Tutoring",
    subtitle: "From Experienced Educators",
    emptyLine: "‎"
  },
  {
    title: "Flexible Online Sessions",
    subtitle: "Learn at Your Own Pace",
    emptyLine: "‎"
  }
];

const AnimatedText = ({ texts }: { texts: typeof heroTexts }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentIndex((prev) => (prev + 1) % texts.length);
        setIsAnimating(false);
      }, 500);
    }, 6000);

    return () => clearInterval(interval);
  }, [texts.length]);

  const containerVariants = {
    hidden: {
      opacity: 0,
      rotateX: -20,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      rotateX: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.43, 0.13, 0.23, 0.96],
        staggerChildren: 0.08
      }
    },
    exit: {
      opacity: 0,
      rotateX: 20,
      scale: 0.95,
      transition: { duration: 0.3 }
    }
  };

  const wordVariants = {
    hidden: {
      opacity: 0,
      y: 20,
      rotateY: -20,
      scale: 0.9
    },
    visible: {
      opacity: 1,
      y: 0,
      rotateY: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    }
  };

  return (
    <div className="h-[180px] md:h-[160px] flex flex-col items-center justify-center overflow-hidden">
      <motion.div
        key={currentIndex}
        variants={containerVariants}
        initial="hidden"
        animate={isAnimating ? "exit" : "visible"}
        className="text-center perspective-[1000px] transform-gpu"
      >
        {/* Empty Line */}
        <motion.div
          className="h-6 md:h-8"
          variants={wordVariants}
        >
          {texts[currentIndex].emptyLine}
        </motion.div>

        {/* Main Title */}
        <h1 className="text-4xl md:text-6xl font-extrabold mb-8 relative">
          <div className="overflow-hidden">
            <motion.div className="flex flex-wrap justify-center gap-x-3">
              {texts[currentIndex].title.split(' ').map((word, i) => (
                <motion.div
                  key={i}
                  className="relative inline-block perspective-[1000px] transform-gpu"
                  variants={wordVariants}
                >
                  <motion.span
                    className="inline-block relative text-[#003049] drop-shadow-[0_2px_2px_rgba(0,0,0,0.1)]
                             [text-shadow:_2px_2px_0_#fff,_4px_4px_0_rgba(0,48,73,0.1)]
                             hover:text-[#F77F00] transition-colors duration-300"
                    whileHover={{
                      scale: 1.05,
                      y: -5,
                      transition: {
                        type: "spring",
                        stiffness: 300
                      }
                    }}
                  >
                    {word}
                  </motion.span>
                </motion.div>
              ))}
            </motion.div>
          </div>

          {/* Background glow effect */}
          <motion.div
            className="absolute -inset-4 rounded-2xl opacity-75 -z-10"
            style={{
              background: 'radial-gradient(circle at center, rgba(252,191,73,0.2) 0%, rgba(247,127,0,0.1) 50%, transparent 100%)',
              filter: 'blur(20px)'
            }}
          />
        </h1>

        {/* Subtitle */}
        <motion.h2
          variants={wordVariants}
          className="text-2xl md:text-3xl font-bold mb-8 relative"
        >
          <span className="relative inline-block
                         text-[#003049]
                         drop-shadow-[0_2px_2px_rgba(0,0,0,0.05)]">
            {texts[currentIndex].subtitle}

            {/* Animated underline */}
            <motion.div
              className="absolute -bottom-2 left-0 w-full h-1 rounded-full bg-[#F77F00]"
              initial={{ scaleX: 0, opacity: 0 }}
              animate={{
                scaleX: 1,
                opacity: 1,
                transition: {
                  duration: 0.6,
                  delay: 0.2,
                  ease: "easeOut"
                }
              }}
            />

            {/* Subtle glow effect */}
            <motion.div
              className="absolute -inset-2 rounded-lg opacity-25"
              style={{
                background: 'radial-gradient(circle at center, rgba(247,127,0,0.2) 0%, transparent 70%)',
                filter: 'blur(8px)'
              }}
              animate={{
                opacity: [0.25, 0.4, 0.25],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </span>
        </motion.h2>
      </motion.div>
    </div>
  );
};

const Particle = ({ index }: { index: number }) => {
  const radius = Math.random() * 100 + 50;
  const angle = (index * 2 * Math.PI) / 12;
  const size = Math.random() * 8 + 4; // Random size between 4-12px

  return (
    <motion.div
      className="absolute rounded-full"
      style={{
        width: `${size}px`,
        height: `${size}px`,
        background: 'radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 40%, rgba(247, 127, 0, 0.05) 60%, rgba(255, 255, 255, 0.1) 100%)',
        boxShadow: `
          inset -2px -2px 4px rgba(0, 0, 0, 0.1),
          inset 2px 2px 4px rgba(255, 255, 255, 0.5),
          0 0 8px rgba(255, 255, 255, 0.3)
        `,
        backdropFilter: 'blur(2px)',
      }}
      initial={{
        x: Math.cos(angle) * radius,
        y: Math.sin(angle) * radius,
        opacity: 0,
        scale: 0.5,
      }}
      animate={{
        x: [
          Math.cos(angle) * radius,
          Math.cos(angle + Math.PI) * (radius + 20),
          Math.cos(angle) * radius,
        ],
        y: [
          Math.sin(angle) * radius,
          Math.sin(angle + Math.PI) * (radius + 20),
          Math.sin(angle) * radius,
        ],
        opacity: [0, 1, 0],
        scale: [0.5, 1, 0.5],
        rotate: [0, 360],
      }}
      transition={{
        duration: 4 + Math.random() * 2,
        repeat: Infinity,
        delay: index * 0.3,
        ease: "easeInOut",
      }}
    >
      {/* Bubble shine effect */}
      <div
        className="absolute w-[30%] h-[30%] rounded-full"
        style={{
          background: 'radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 100%)',
          top: '20%',
          left: '20%',
          transform: 'rotate(-45deg)',
        }}
      />
    </motion.div>
  );
};

const FloatingBubble = ({ index }: { index: number }) => {
  const size = Math.random() * 6 + 3; // Random size between 3-9px
  return (
    <motion.div
      className="absolute rounded-full"
      style={{
        width: `${size}px`,
        height: `${size}px`,
        background: 'radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 40%, rgba(247, 127, 0, 0.05) 60%, rgba(255, 255, 255, 0.1) 100%)',
        boxShadow: `
          inset -1px -1px 2px rgba(0, 0, 0, 0.1),
          inset 1px 1px 2px rgba(255, 255, 255, 0.5),
          0 0 4px rgba(255, 255, 255, 0.3)
        `,
      }}
      initial={{
        x: Math.random() * 200 - 100,
        y: Math.random() * 200 - 100,
        scale: 0,
        opacity: 0,
      }}
      animate={{
        x: Math.random() * 200 - 100,
        y: Math.random() * 200 - 100,
        scale: [0, 1, 0],
        opacity: [0, 0.7, 0],
        rotate: 360,
      }}
      transition={{
        duration: 3 + Math.random() * 2,
        repeat: Infinity,
        delay: index * 0.5,
        ease: "easeInOut",
      }}
    >
      <div
        className="absolute w-[30%] h-[30%] rounded-full"
        style={{
          background: 'radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 100%)',
          top: '20%',
          left: '20%',
          transform: 'rotate(-45deg)',
        }}
      />
    </motion.div>
  );
};

// Enhanced Particle Component with more dynamic effects
const EnhancedParticle = ({ index }: { index: number }) => {
  const radius = Math.random() * 120 + 80;
  const angle = (index * 2 * Math.PI) / 20;
  const size = Math.random() * 12 + 6;
  const colors = ['#F77F00', '#FCBF49', '#003049', '#D62828', '#EAE2B7'];
  const color = colors[index % colors.length];

  return (
    <motion.div
      className="absolute rounded-full"
      style={{
        width: `${size}px`,
        height: `${size}px`,
        background: `radial-gradient(circle at 30% 30%, ${color}CC 0%, ${color}66 40%, ${color}22 60%, transparent 100%)`,
        boxShadow: `
          inset -2px -2px 4px rgba(0, 0, 0, 0.2),
          inset 2px 2px 4px rgba(255, 255, 255, 0.8),
          0 0 16px ${color}66,
          0 0 32px ${color}33
        `,
        backdropFilter: 'blur(4px)',
      }}
      initial={{
        x: Math.cos(angle) * radius,
        y: Math.sin(angle) * radius,
        opacity: 0,
        scale: 0.3,
      }}
      animate={{
        x: [
          Math.cos(angle) * radius,
          Math.cos(angle + Math.PI * 0.5) * (radius + 40),
          Math.cos(angle + Math.PI) * (radius + 20),
          Math.cos(angle + Math.PI * 1.5) * (radius + 40),
          Math.cos(angle) * radius,
        ],
        y: [
          Math.sin(angle) * radius,
          Math.sin(angle + Math.PI * 0.5) * (radius + 40),
          Math.sin(angle + Math.PI) * (radius + 20),
          Math.sin(angle + Math.PI * 1.5) * (radius + 40),
          Math.sin(angle) * radius,
        ],
        opacity: [0, 0.8, 1, 0.8, 0],
        scale: [0.3, 1.2, 1, 1.2, 0.3],
        rotate: [0, 180, 360, 540, 720],
      }}
      transition={{
        duration: 8 + Math.random() * 4,
        repeat: Infinity,
        delay: index * 0.2,
        ease: "easeInOut",
      }}
    >
      {/* Enhanced shine effect */}
      <div
        className="absolute w-[40%] h-[40%] rounded-full"
        style={{
          background: 'radial-gradient(circle, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.3) 50%, transparent 100%)',
          top: '15%',
          left: '15%',
          transform: 'rotate(-45deg)',
        }}
      />
      {/* Secondary glow */}
      <div
        className="absolute w-[20%] h-[20%] rounded-full"
        style={{
          background: `radial-gradient(circle, ${color}FF 0%, transparent 100%)`,
          top: '60%',
          left: '60%',
          filter: 'blur(2px)',
        }}
      />
    </motion.div>
  );
};

// Enhanced Energy Wave Component
const EnergyWave = ({ index }: { index: number }) => {
  const colors = ['#F77F00', '#FCBF49', '#D62828'];
  const color = colors[index % colors.length];

  return (
    <motion.div
      className="absolute rounded-full border-2"
      style={{
        width: '300px',
        height: '300px',
        borderColor: `${color}44`,
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)',
      }}
      initial={{
        scale: 0.5,
        opacity: 0,
      }}
      animate={{
        scale: [0.5, 2.5, 3],
        opacity: [0, 0.6, 0],
        borderColor: [`${color}44`, `${color}88`, `${color}00`],
      }}
      transition={{
        duration: 4,
        repeat: Infinity,
        delay: index * 1.5,
        ease: "easeOut",
      }}
    />
  );
};

const LogoAnimation = () => {
  const [mouseX, setMouseX] = useState(0);
  const [mouseY, setMouseY] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const controls = useAnimation();

  const handleMouseMove = (e: React.MouseEvent) => {
    const { left, top, width, height } = e.currentTarget.getBoundingClientRect();
    const x = (e.clientX - left) / width - 0.5;
    const y = (e.clientY - top) / height - 0.5;
    setMouseX(x);
    setMouseY(y);
  };

  useEffect(() => {
    controls.start({
      rotateY: mouseX * 25,
      rotateX: -mouseY * 25,
      scale: isHovered ? 1.05 : 1,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 25,
        opacity: { duration: 1, ease: "easeOut" },
        scale: { duration: 0.4, ease: "easeOut" }
      }
    });
  }, [mouseX, mouseY, isHovered, controls]);

  return (
    <div
      className="relative inline-block perspective-1000"
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => {
        setMouseX(0);
        setMouseY(0);
        setIsHovered(false);
      }}
    >
      <motion.div
        className="relative"
        animate={controls}
        initial={{ scale: 0.8, opacity: 0 }}
      >
        {/* Enhanced Particles */}
        <div className="absolute inset-0 flex items-center justify-center">
          {Array.from({ length: 20 }).map((_, i) => (
            <EnhancedParticle key={i} index={i} />
          ))}
        </div>

        {/* Energy Waves */}
        <div className="absolute inset-0 flex items-center justify-center">
          {Array.from({ length: 3 }).map((_, i) => (
            <EnergyWave key={i} index={i} />
          ))}
        </div>

        {/* Enhanced Multi-layered Background Effects */}
        <motion.div
          className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
          style={{ zIndex: 1, width: 320, height: 320 }}
        >
          {/* Primary Glow Layer */}
          <motion.div
            className="absolute inset-0 rounded-full"
            style={{
              background: 'radial-gradient(circle, #F77F0088 0%, #FCBF4966 30%, #00304944 60%, transparent 100%)',
              filter: 'blur(40px)',
            }}
            animate={{
              scale: [1, 1.15, 1],
              opacity: [0.7, 1, 0.7],
              rotate: [0, 360],
            }}
            transition={{ duration: 8, repeat: Infinity, ease: 'easeInOut' }}
          />

          {/* Secondary Color Layer */}
          <motion.div
            className="absolute inset-4 rounded-full"
            style={{
              background: 'radial-gradient(circle, #FCBF49AA 0%, #F77F0088 50%, transparent 100%)',
              filter: 'blur(30px)',
            }}
            animate={{
              scale: [1.1, 0.9, 1.1],
              opacity: [0.8, 0.5, 0.8],
              rotate: [360, 0],
            }}
            transition={{ duration: 6, repeat: Infinity, ease: 'easeInOut' }}
          />



          {/* Enhanced Glassmorphism Panel */}
          <motion.div
            className="absolute inset-12 rounded-full backdrop-blur-2xl shadow-2xl"
            style={{
              background: 'linear-gradient(135deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0.1) 100%)',
              border: '2px solid rgba(255,255,255,0.3)',
              zIndex: 2
            }}
            animate={{
              background: [
                'linear-gradient(135deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0.1) 100%)',
                'linear-gradient(135deg, rgba(247,127,0,0.3) 0%, rgba(252,191,73,0.2) 100%)',
                'linear-gradient(135deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0.1) 100%)',
              ]
            }}
            transition={{ duration: 5, repeat: Infinity, ease: 'easeInOut' }}
          />

          {/* Dynamic Light Sweep */}
          <motion.div
            className="absolute inset-0 rounded-full pointer-events-none"
            style={{
              background: 'conic-gradient(from 0deg, transparent 0%, #F77F0088 20%, #FCBF4988 40%, transparent 60%, #D6282888 80%, transparent 100%)',
              mixBlendMode: 'screen',
              opacity: 0.6,
              zIndex: 3,
            }}
            animate={{
              rotate: [0, 360],
              opacity: [0.6, 0.9, 0.6],
            }}
            transition={{ duration: 8, repeat: Infinity, ease: 'linear' }}
          />
        </motion.div>

        {/* Enhanced Logo with Morphing Effects */}
        <motion.div
          className="relative transform-gpu"
          style={{ zIndex: 4 }}
          whileHover={{
            scale: 1.1,
            filter: 'brightness(1.1)',
            transition: { duration: 0.3 }
          }}
          animate={{
            y: [0, -5, 0],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <motion.img
            src="https://ik.imagekit.io/quadrate/Studytomy/Studytomy_Logobook-02.png?updatedAt=1731862139834"
            alt="Studytomy Logo"
            className="relative mx-auto w-[220px] mb-16"
            style={{
              filter: "drop-shadow(0 8px 40px #00304933)",
            }}
          />
        </motion.div>

        {/* Enhanced Floating Elements */}
        <div className="absolute inset-0">
          {Array.from({ length: 12 }).map((_, i) => (
            <FloatingBubble key={i} index={i} />
          ))}
        </div>
      </motion.div>
    </div>
  );
};


// Enhanced Floating Sphere Component
function FloatingSphere({ initialPosition, index }: { initialPosition: [number, number, number]; index: number }) {
  const meshRef = useRef<THREE.Mesh>(null);
  const [targetPosition, setTargetPosition] = useState(new THREE.Vector3(...initialPosition));
  const currentPosition = useRef(new THREE.Vector3(...initialPosition));

  // Enhanced color palette with gradients
  const colors = [
    "#F77F00", // Orange Peel
    "#FCBF49", // Maize
    "#D62828", // Cinnabar
    "#003049", // Prussian Blue
    "#EAE2B7"  // Bone
  ];

  const sphereColor = colors[index % colors.length];

  const getRandomPosition = () => {
    return new THREE.Vector3(
      (Math.random() - 0.5) * 30,
      Math.random() * 8 + 1,
      (Math.random() - 0.5) * 30
    );
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setTargetPosition(getRandomPosition());
    }, 3000 + Math.random() * 2000);

    return () => clearInterval(interval);
  }, []);

  useFrame((state, delta) => {
    if (meshRef.current) {
      currentPosition.current.lerp(targetPosition, 0.02);
      meshRef.current.position.copy(currentPosition.current);

      // Add floating animation
      meshRef.current.position.y += Math.sin(state.clock.elapsedTime * 0.5 + index) * 0.1;

      // Add rotation
      meshRef.current.rotation.x += delta * 0.2;
      meshRef.current.rotation.y += delta * 0.3;
    }
  });

  return (
    <mesh ref={meshRef} position={initialPosition}>
      <sphereGeometry args={[0.8, 16, 16]} />
      <meshStandardMaterial
        color={sphereColor}
        opacity={0.7}
        transparent
        emissive={sphereColor}
        emissiveIntensity={0.2}
        roughness={0.3}
        metalness={0.1}
      />
    </mesh>
  );
}

// Enhanced Animated Box with better movement
function AnimatedBox({ initialPosition, index }: { initialPosition: [number, number, number]; index: number }) {
  const meshRef = useRef<THREE.Mesh>(null);
  const [targetPosition, setTargetPosition] = useState(new THREE.Vector3(...initialPosition));
  const currentPosition = useRef(new THREE.Vector3(...initialPosition));

  // Primary colors from the color palette
  const primaryColors = [
    "#003049", // Prussian Blue
    "#D62828", // Cinnabar
    "#F77F00", // Orange Peel
    "#FCBF49", // Maize
    "#EAE2B7"  // Bone
  ];

  // Edge colors (using a complementary color from the palette)
  const edgeColors = [
    "#FCBF49", // Maize
    "#EAE2B7", // Bone
    "#003049", // Prussian Blue
    "#D62828", // Cinnabar
    "#F77F00"  // Orange Peel
  ];

  // Select color based on index
  const cubeColor = primaryColors[index % primaryColors.length];
  const edgeColor = edgeColors[index % edgeColors.length];

  const getAdjacentIntersection = (current: THREE.Vector3) => {
    const directions = [[1, 0], [-1, 0], [0, 1], [0, -1], [1, 1], [-1, -1], [1, -1], [-1, 1]];
    const randomDirection = directions[Math.floor(Math.random() * directions.length)];
    return new THREE.Vector3(
      current.x + randomDirection[0] * 4,
      0.5 + Math.random() * 2,
      current.z + randomDirection[1] * 4
    );
  };

  useEffect(() => {
    const interval = setInterval(() => {
      const newPosition = getAdjacentIntersection(currentPosition.current);
      newPosition.x = Math.max(-20, Math.min(20, newPosition.x));
      newPosition.z = Math.max(-20, Math.min(20, newPosition.z));
      setTargetPosition(newPosition);
    }, 2000 + Math.random() * 1000);

    return () => clearInterval(interval);
  }, []);

  useFrame((state, delta) => {
    if (meshRef.current) {
      currentPosition.current.lerp(targetPosition, 0.05);
      meshRef.current.position.copy(currentPosition.current);

      // Add subtle rotation
      meshRef.current.rotation.x += delta * 0.1;
      meshRef.current.rotation.y += delta * 0.15;
      meshRef.current.rotation.z += delta * 0.05;
    }
  });

  return (
    <mesh ref={meshRef} position={initialPosition}>
      <boxGeometry args={[1.2, 1.2, 1.2]} />
      <meshStandardMaterial
        color={cubeColor}
        opacity={0.8}
        transparent
        emissive={cubeColor}
        emissiveIntensity={0.1}
        roughness={0.4}
        metalness={0.2}
      />
      <lineSegments>
        <edgesGeometry attach="geometry" args={[new THREE.BoxGeometry(1.2, 1.2, 1.2)]} />
        <lineBasicMaterial attach="material" color={edgeColor} linewidth={3} />
      </lineSegments>
    </mesh>
  );
}

// Enhanced Camera Controller
function CameraController() {
  useFrame((state) => {
    // Smooth camera movement
    const time = state.clock.elapsedTime * 0.1;
    state.camera.position.x = Math.sin(time) * 5 + 30;
    state.camera.position.z = Math.cos(time) * 5 + 30;
    state.camera.lookAt(0, 0, 0);
  });

  return null;
}

function Scene() {
  const boxPositions: [number, number, number][] = [
    [-9, 0.5, -9], [-3, 0.5, -3], [0, 0.5, 0],
    [3, 0.5, 3], [9, 0.5, 9], [-6, 0.5, 6],
    [6, 0.5, -6], [-12, 0.5, 0], [12, 0.5, 0],
    [0, 0.5, 12]
  ];

  const spherePositions: [number, number, number][] = [
    [-15, 3, -15], [15, 4, 15], [-10, 2, 10],
    [10, 5, -10], [0, 6, -20], [-20, 3, 0],
    [20, 4, 5], [5, 2, 20]
  ];

  return (
    <>
      <OrbitControls enableZoom={false} enablePan={false} autoRotate autoRotateSpeed={0.5} />
      <CameraController />

      {/* Enhanced Lighting Setup */}
      <ambientLight intensity={0.4} color="#EAE2B7" />
      <directionalLight
        position={[10, 10, 10]}
        intensity={0.8}
        color="#FCBF49"
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      <pointLight position={[-10, 15, -10]} intensity={0.6} color="#F77F00" />
      <pointLight position={[15, 8, 15]} intensity={0.5} color="#D62828" />
      <spotLight
        position={[0, 20, 0]}
        intensity={0.7}
        color="#003049"
        angle={Math.PI / 6}
        penumbra={0.5}
        castShadow
      />

      {/* Enhanced Grid */}
      <Grid
        renderOrder={-1}
        position={[0, 0, 0]}
        infiniteGrid
        cellSize={1.5}
        cellThickness={0.8}
        sectionSize={4.5}
        sectionThickness={1.5}
        sectionColor="#F77F00"
        cellColor="#FCBF49"
        fadeDistance={60}
        fadeStrength={0.8}
      />

      {/* Animated Boxes */}
      {boxPositions.map((position, index) => (
        <AnimatedBox key={`box-${index}`} initialPosition={position} index={index} />
      ))}

      {/* Floating Spheres */}
      {spherePositions.map((position, index) => (
        <FloatingSphere key={`sphere-${index}`} initialPosition={position} index={index} />
      ))}
    </>
  );
}


export default function Hero() {
  return (
    <section className="relative min-h-screen overflow-hidden">
      {/* Enhanced Animated Background */}
      <div className="absolute inset-0 -z-20">
        {/* Multi-layered gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#E3F2FD] via-[#BBDEFB] to-[#90CAF9]" />
        <div className="absolute inset-0 bg-gradient-to-tr from-[#FFF3E0]/30 via-transparent to-[#FFE0B2]/20" />
        <div className="absolute inset-0 bg-gradient-to-bl from-transparent via-[#F3E5F5]/20 to-[#E1BEE7]/30" />

        {/* Enhanced 3D Scene */}
        <Canvas shadows camera={{ position: [30, 30, 30], fov: 50 }}>
          <Scene />
        </Canvas>

        {/* Vibrant subject icons and globe animation */}
        <HeroBackgroundSubjects />

        {/* Additional animated overlay */}
        <motion.div
          className="absolute inset-0 opacity-30"
          animate={{
            background: [
              'radial-gradient(circle at 20% 80%, #F77F0020 0%, transparent 50%)',
              'radial-gradient(circle at 80% 20%, #FCBF4920 0%, transparent 50%)',
              'radial-gradient(circle at 40% 40%, #D6282820 0%, transparent 50%)',
              'radial-gradient(circle at 20% 80%, #F77F0020 0%, transparent 50%)',
            ]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      {/* Content */}
      <div className="relative z-10 pt-16 pb-20">
        <div className="container mx-auto px-4 text-center mb-12">
          {/* Logo Section with negative margin */}
          <div className="relative -mb-16">
            <LogoAnimation />
          </div>

          {/* Hero Text Section with higher z-index */}
          <div className="relative z-20">
            <AnimatedText texts={heroTexts} />
            <HeroButton />
          </div>
        </div>

        {/* Enhanced Features Grid */}
        <div className="container mx-auto px-4 mt-16">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              className="text-center bg-white/90 backdrop-blur-lg rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 border border-white/20"
              initial={{ opacity: 0, scale: 0.5, y: 50 }}
              animate={{
                opacity: 1,
                scale: 1,
                y: 0,
                transition: {
                  delay: 0.2,
                  type: "spring",
                  stiffness: 100,
                  damping: 10
                }
              }}
              whileHover={{
                scale: 1.05,
                y: -10,
                boxShadow: "0 25px 50px -12px rgba(247, 127, 0, 0.25)"
              }}
              whileTap={{ scale: 0.95 }}
            >
              <motion.div
                className="flex justify-center mb-4"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
              >
                <GraduationCap className="h-16 w-16 text-[#F77F00] drop-shadow-lg" />
              </motion.div>
              <h3 className="mt-4 text-xl font-bold text-[#003049]">Expert Tutors</h3>
              <p className="mt-3 text-base text-gray-700 leading-relaxed">Qualified teachers from top institutions worldwide</p>
            </motion.div>

            <motion.div
              className="text-center bg-white/90 backdrop-blur-lg rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 border border-white/20"
              initial={{ opacity: 0, scale: 0.5, y: 50 }}
              animate={{
                opacity: 1,
                scale: 1,
                y: 0,
                transition: {
                  delay: 0.4,
                  type: "spring",
                  stiffness: 100,
                  damping: 10
                }
              }}
              whileHover={{
                scale: 1.05,
                y: -10,
                boxShadow: "0 25px 50px -12px rgba(252, 191, 73, 0.25)"
              }}
              whileTap={{ scale: 0.95 }}
            >
              <motion.div
                className="flex justify-center mb-4"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
              >
                <Users className="h-16 w-16 text-[#FCBF49] drop-shadow-lg" />
              </motion.div>
              <h3 className="mt-4 text-xl font-bold text-[#003049]">1-on-1 Sessions</h3>
              <p className="mt-3 text-base text-gray-700 leading-relaxed">Personalized attention and detailed feedback</p>
            </motion.div>

            <motion.div
              className="text-center bg-white/90 backdrop-blur-lg rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 border border-white/20"
              initial={{ opacity: 0, scale: 0.5, y: 50 }}
              animate={{
                opacity: 1,
                scale: 1,
                y: 0,
                transition: {
                  delay: 0.6,
                  type: "spring",
                  stiffness: 100,
                  damping: 10
                }
              }}
              whileHover={{
                scale: 1.05,
                y: -10,
                boxShadow: "0 25px 50px -12px rgba(214, 40, 40, 0.25)"
              }}
              whileTap={{ scale: 0.95 }}
            >
              <motion.div
                className="flex justify-center mb-4"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
              >
                <Globe2 className="h-16 w-16 text-[#D62828] drop-shadow-lg" />
              </motion.div>
              <h3 className="mt-4 text-xl font-bold text-[#003049]">Global Reach</h3>
              <p className="mt-3 text-base text-gray-700 leading-relaxed">Connect with expert tutors worldwide</p>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Enhanced Particles */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(15)].map((_, i) => (
          <Particle key={`particle-${i}`} index={i} />
        ))}
        {[...Array(25)].map((_, i) => (
          <FloatingBubble key={`bubble-${i}`} index={i} />
        ))}
      </div>
    </section>
  );
}
